SELECT
	g.goods_sku AS goods_sku,
	g.goods_name AS goods_name,
	dgs.goods_unit AS goods_unit,
	gdl.detail AS detail_lang_cn,
	dgss.stock_user_id AS stock_user_id,
	dgss.stock_user AS stock_user,
	g.develop_user_id AS develop_user_id,
	g.develop_user AS develop_user,
	g.create_user_id AS create_user_id,
	g.admin_job_id AS admin_job_id,
	dgss.remarks AS remarks,
	g.goods_stock AS goods_stock,
	dgss.delivery_time AS delivery_time,
	gah.length AS length,
	gah.width AS width,
	gah.height AS height,
	gah.weight_net AS weight_net,
	gah.weight AS weight,
	gah.box_length AS box_length,
	gah.box_width AS box_width,
	gah.box_height AS box_height,
	gah.box_weight_net AS box_weight_net,
	gah.box_num AS box_num,
	gde.goods_declare AS goods_declare,
	gde.goods_inland_code AS goods_inland_code,
	gde.goods_declare_en AS goods_declare_en,
	gde.goods_declare_usd AS goods_declare_usd,
	g.goods_attribute AS goods_attribute,-- 假设这是一个可直接获取的属性或需要进一步处理的字段
	gde.goods_declare_company AS goods_declare_company,
	dgss.currency AS currency,
	dgss.tax AS tax
FROM
	gms.goods g
	LEFT JOIN gms.dev_goods_sku dgs ON g.id = dgs.goods_id
	LEFT JOIN gms.goods_detail gdl ON g.id = gdl.goods_id
	AND gdl.lang = 'cn'
	LEFT JOIN gms.dev_goods_sku_stock dgss ON g.id = dgss.goods_id
	LEFT JOIN gms.goods_attach gah ON g.id = gah.goods_id
	LEFT JOIN gms.goods_declare gde ON g.id = gde.goods_id