# -*- coding:UTF-8 -*-
# @FileName  :test_single_retry.py
# @Time      :2025/1/26 
# <AUTHOR>

"""
测试单个记录的重新获取修复逻辑
使用场景：手动在数据库中将某些记录的 total 改为 0，但保持 history_total 不为 0，然后测试重新获取功能
"""

import sys
sys.path.append(r"E:\Project_py")
from 自动化.领星抓取.LingXingSessionPage import (
    get_failed_inventory_records,
    retry_fetch_inventory_by_fnsku,
    retry_fetch_inventory_by_msku,
    update_inventory_record,
    LingXingSessionPage
)

def test_get_failed_records():
    """
    测试获取失败记录
    前提：您需要先在数据库中手动将一些记录的 total 改为 0，但保持 history_total 不为 0
    """
    print("=== 步骤1: 获取失败记录 ===")
    try:
        failed_records = get_failed_inventory_records()
        print(f"找到 {len(failed_records)} 条需要修复的记录")
        
        if failed_records:
            print("\n失败记录详情:")
            for i, record in enumerate(failed_records[:10]):  # 只显示前10条
                print(f"  {i+1}. ID: {record.get('id')}")
                print(f"      FNSKU: {record.get('fnsku')}")
                print(f"      MSKU: {record.get('msku')}")
                print(f"      ASIN: {record.get('asin')}")
                print(f"      当前Total: {record.get('total')}")
                print(f"      历史Total: {record.get('history_total')}")
                print(f"      商品名: {record.get('name', '')[:50]}...")
                print(f"      店铺: {record.get('seller_name', '')}")
                print("      " + "-"*50)
        
        return failed_records
    except Exception as e:
        print(f"获取失败记录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_single_retry_by_fnsku(record, user='jszg01'):
    """
    测试通过 FNSKU 重新获取单个记录
    """
    print(f"\n=== 步骤2: 通过FNSKU重新获取记录 ID:{record['id']} ===")
    
    fnsku = record.get('fnsku', '')
    if not fnsku:
        print("该记录没有FNSKU，跳过测试")
        return None
    
    try:
        # 创建会话
        lx_session = LingXingSessionPage()
        lx_session.user = user
        print(f"使用用户 {user} 创建会话成功")
        
        # 重新获取数据
        print(f"正在通过FNSKU '{fnsku}' 重新获取数据...")
        inventory_data = retry_fetch_inventory_by_fnsku(lx_session, fnsku, max_retries=2)
        
        if inventory_data:
            print("✅ 重新获取成功!")
            print(f"   新的Total: {inventory_data.get('total', 0)}")
            print(f"   Available Total: {inventory_data.get('available_total', 0)}")
            print(f"   AFN Fulfillable: {inventory_data.get('afn_fulfillable_quantity', 0)}")
            return inventory_data
        else:
            print("❌ 通过FNSKU重新获取失败")
            return None
            
    except Exception as e:
        print(f"通过FNSKU重新获取时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_single_retry_by_msku(record, user='jszg01'):
    """
    测试通过 MSKU 重新获取单个记录
    """
    print(f"\n=== 步骤3: 通过MSKU重新获取记录 ID:{record['id']} ===")
    
    msku = record.get('msku', '')
    if not msku:
        print("该记录没有MSKU，跳过测试")
        return None
    
    try:
        # 创建会话
        lx_session = LingXingSessionPage()
        lx_session.user = user
        print(f"使用用户 {user} 创建会话成功")
        
        # 重新获取数据
        print(f"正在通过MSKU '{msku}' 重新获取数据...")
        inventory_data = retry_fetch_inventory_by_msku(lx_session, msku, max_retries=2)
        
        if inventory_data:
            print("✅ 重新获取成功!")
            print(f"   新的Total: {inventory_data.get('total', 0)}")
            print(f"   Available Total: {inventory_data.get('available_total', 0)}")
            print(f"   AFN Fulfillable: {inventory_data.get('afn_fulfillable_quantity', 0)}")
            return inventory_data
        else:
            print("❌ 通过MSKU重新获取失败")
            return None
            
    except Exception as e:
        print(f"通过MSKU重新获取时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_update_record(record_id, inventory_data):
    """
    测试更新记录
    """
    print(f"\n=== 步骤4: 更新数据库记录 ID:{record_id} ===")
    
    if not inventory_data:
        print("没有新数据，跳过更新")
        return False
    
    try:
        print("正在更新数据库记录...")
        success = update_inventory_record(record_id, inventory_data)
        
        if success:
            print("✅ 数据库更新成功!")
            return True
        else:
            print("❌ 数据库更新失败")
            return False
            
    except Exception as e:
        print(f"更新数据库记录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_single_fix(record, user='jszg01'):
    """
    测试完整的单个记录修复流程
    """
    print(f"\n{'='*60}")
    print(f"开始测试完整修复流程 - 记录ID: {record['id']}")
    print(f"{'='*60}")
    
    record_id = record['id']
    fnsku = record.get('fnsku', '')
    msku = record.get('msku', '')
    
    print(f"记录信息:")
    print(f"  ID: {record_id}")
    print(f"  FNSKU: {fnsku}")
    print(f"  MSKU: {msku}")
    print(f"  当前Total: {record.get('total')}")
    print(f"  历史Total: {record.get('history_total')}")
    
    # 尝试通过FNSKU获取
    inventory_data = None
    if fnsku:
        inventory_data = test_single_retry_by_fnsku(record, user)
    
    # 如果FNSKU失败，尝试MSKU
    if not inventory_data and msku:
        inventory_data = test_single_retry_by_msku(record, user)
    
    # 更新数据库
    if inventory_data:
        success = test_update_record(record_id, inventory_data)
        if success:
            print(f"\n🎉 记录 ID:{record_id} 修复完成!")
            return True
        else:
            print(f"\n❌ 记录 ID:{record_id} 修复失败 - 数据库更新失败")
            return False
    else:
        print(f"\n❌ 记录 ID:{record_id} 修复失败 - 无法重新获取数据")
        return False

def main():
    """
    主测试函数
    """
    print("开始测试单个记录重新获取修复逻辑...")
    print("\n📋 测试前准备:")
    print("1. 请先在数据库中手动将一些记录的 total 字段改为 0")
    print("2. 确保这些记录的 history_total 字段不为 0")
    print("3. 记录这些记录的 ID、FNSKU、MSKU 等信息")
    print("\n开始测试...")
    
    # 获取失败记录
    failed_records = test_get_failed_records()
    
    if not failed_records:
        print("\n❌ 没有找到需要修复的记录")
        print("请先在数据库中手动创建一些测试数据:")
        print("UPDATE idc.lingxing_fba_inventory SET total = 0 WHERE id IN (具体的ID) AND history_total > 0;")
        return
    
    # 选择第一条记录进行测试
    test_record = failed_records[0]
    print(f"\n选择第一条记录进行测试: ID {test_record['id']}")
    
    # 执行完整修复流程
    success = test_complete_single_fix(test_record, user='jszg01')
    
    if success:
        print(f"\n🎉 单个记录修复测试成功!")
    else:
        print(f"\n❌ 单个记录修复测试失败")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 用户权限问题")
        print("3. FNSKU/MSKU 在领星系统中不存在")
        print("4. 数据库连接问题")
    
    print(f"\n测试完成!")

if __name__ == '__main__':
    main()
