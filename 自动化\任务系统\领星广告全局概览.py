# -*- coding:UTF-8 -*-
# @FileName  :领星广告全局概览采集.py
# @Time      :2024年11月13日18:35:56
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.领星抓取.LingXing import *

# globals()['MS'] = MysqlHelper()


def main(wait_hour=2):
    while True:
        # current_hour = datetime.now().hour
        # if not (0 <= current_hour < 7):
        #     create_task()
        # else:
        #     logging('当前时间在凌晨0-6点之间，跳过任务执行')
        main_fetch_ad_analyze()
        time.sleep(60 * 60 * wait_hour)  # 每两小时检查一次


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    while True:
        try:
            main(wait_hour=2)
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send(None, msg, err)
