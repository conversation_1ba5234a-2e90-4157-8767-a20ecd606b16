from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

file = r'D:\Documents\pythonProject\work\自动化\领星抓取\广告店铺站点.xlsx'
MS = MSXS
# ep = ExcelProcessor(file)
df = pd.read_excel(file)

store = tuple(df['店铺'].tolist())
# print(store)
rs = MS.get_all('select * from data_lingxing_account where alias in %s', (store,))
# print(rs)
alias = [i[4] for i in rs]
rs = MS.update('update data_lingxing_account set status=99')
rs = MS.update('update data_lingxing_account set status=0 where alias in %s', (store,))
# rs = MS.update('update data_lingxing_account set status=99 where alias in %s', (store,))
print(rs)
