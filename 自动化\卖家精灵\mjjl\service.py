import os
import json
import random
from flask import Flask, jsonify, send_file

app = Flask(__name__)

json_folder_path = "account_data"
json_file_path = 'update/update.json'


@app.route('/check_update')
def check_update():
    try:
        with open(json_file_path, 'r') as file:
            json_data = json.load(file)
        response = {'code': 200, 'data': json_data}
    except FileNotFoundError:
        response = {'code': -1, 'data': None}
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON in file: {json_file_path}")
        print(f"Error details: {e}")
        response = {'code': -1, 'data': None}
    return jsonify(response)


def get_account_data():
    # 获取 JSON 文件夹中所有 JSON 文件
    json_files = [f for f in os.listdir(json_folder_path) if f.endswith('.json')]

    if not json_files:
        # 如果没有 JSON 文件，返回空字典
        return {}

    # 随机选择一个 JSON 文件
    random_file = random.choice(json_files)
    json_file_path = os.path.join(json_folder_path, random_file)

    # 读取 JSON 文件内容
    with open(json_file_path, 'r') as json_file:
        try:
            json_data = json.load(json_file)
            return json_data
        except json.JSONDecodeError:
            print(f"Error decoding JSON in file: {json_file_path}")
            return {}


@app.route('/get_account_data')
def get_account_list():
    account_data = get_account_data()

    response_data = account_data

    return jsonify(response_data)


if __name__ == '__main__':
    app.run(host="0.0.0.0", port=1788, debug=True)
