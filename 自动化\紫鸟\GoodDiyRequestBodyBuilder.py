import json
import uuid
from collections import defaultdict


def decorators(func):
    def wrapper(*args, **kwargs):
        if len(args) > 1 and isinstance(args[1], dict):
            args = (args[0], defaultdict(lambda: '', args[1]), *args[2:])

        # 调用原始函数
        result_data = func(*args, **kwargs)
        # 过滤函数返回值
        filtered_result_data = filter_result_data(result_data)
        return filtered_result_data

    return wrapper


class GoodDiyRequestBodyBuilder:
    '''
    clientConfigVersion    :     "651010f3-4bf6-4166-9d20-5e06bf780e2e"
    clientConfigVersion    :     "f2348622-5c76-4ab2-8735-2b33bd4f6311"
    '''
    template_identifier_map = {
        "ContainerPickerTemplate": "740ab4ea-9b02-408a-8894-b4ff30298c66",
        "choices.content.PreviewContainerTemplate": "0f0e6e5b-a3ad-4b38-8fa7-fa062e583e24",
        "choices.content.children.ContainerPickerTemplate": "e117c791-a9f1-49d4-ba7b-19d119a02dff",
        "choices.content.children.choices.content.PlacementContainerTemplate": "f600fb96-e3ff-4abf-a654-c6c090441026",
        "choices.content.children.choices.content.children.ImageInputTemplate": "5c3a177a-fee9-4b3c-9c45-f410d04fbb13",
        "choices.content.children.choices.content.children.FontChooserTemplate": "7bf885f6-9f40-40e5-920c-d94c91b580ff",
        "choices.content.children.choices.content.children.ColorChooserTemplate": "0706a30e-8427-45f6-b9a7-3f05915a1bbb",
        "choices.content.children.choices.content.OptionChooserTemplate": "64d67934-cd18-45a3-ada0-8e21386c7418",
        "choices.content.children.choices.content.ContainerTemplate": "4d0c8d52-3282-4cac-8a19-f910a57c7a4b",
        "choices.content.children.choices.content.children.ContainerPickerTemplate": "83b6fdf4-ac4b-4e62-84a2-8a61210de7cc",
        "choices.content.children.choices.content.children.choices.content.PlacementContainerTemplate": "ebed04d3-8194-4596-84d3-4dd975df4c74",
        "choices.content.children.choices.content.children.choices.content.children.FlatRatePriceDeltaContainerTemplate": "54b3d602-9fa1-4bfb-b06d-1de4e3db36ac",
        "choices.content.children.choices.content.children.choices.content.children.children.TextInputTemplate": "c243a0ad-03a9-4e87-aad7-e30b008d7a68",
    }
    allowed_characters = {
        "数字 0 到 9 和空格。不得使用破折号或特殊字符（所有语言）": "da4c5878-6f7a-11ea-bc55-0242ac130003",
        "大写字母 A 到 Z 和空格。不得使用破折号或特殊字符（所有语言）": "549b9874-6f7a-11ea-bc55-0242ac130003",
        "大写字母 A 到 Z 和空格。不得使用破折号或特殊字符（仅限英语）": "577a40f2-3013-4e50-b0dd-02afb82ff0b2",
        "数字 0 到 9 和空格。不得使用破折号或特殊字符（仅限英语）": "bf58e805-0004-442c-9bac-776226269d29",
        "不包括表情符号的所有字符": "1883d0d2-a293-44a7-971b-2fc40388bdb1",
        "任何字母或数字（仅英文）": "bad178a5-b238-4834-977b-5c5763556777",
        "允许使用所有字符，包括表情符号": "3052d23c-90ab-4d30-a117-45624940945b",
        "任何字母或数字（所有语言）": "52ea92fe-6f7b-11ea-bc55-0242ac130003"
    }

    @staticmethod
    def build(json_data):
        return GoodDiyRequestBodyBuilder().reconstruct_data(json_data)

    def __init__(self):
        self.task_content = None
        self.sku = None
        self.site = None
        self.store = None
        self.err = None
        self.json_data = None
        self.type_counter = {}
        self.GoogleFonts = [
            'Abel', 'Abril Fatface', 'Acme', 'Alegreya', 'Alfa Slab One', 'Allura', 'Amatic SC', 'Antic Slab', 'Anton', 'Archivo Black', 'Arimo', 'Arvo',
            'Asap Condensed', 'Audiowide', 'Bad Script', 'Baloo', 'Baloo Bhai', 'Bangers', 'Bebas Neue', 'Bowlby One SC', 'Bree Serif', 'Cabin Sketch',
            'Cantata One', 'Caveat', 'Chewy', 'Cinzel', 'Comfortaa', 'Cookie', 'Cormorant Garamond', 'Courgette', 'Covered By Your Grace', 'Damion',
            'Dancing Script', 'Days One', 'Didact Gothic', 'EB Garamond', 'Encode Sans', 'Fauna One', 'Fjalla One', 'Fondamento', 'Forum', 'Fredericka the Great',
            'Fredoka One', 'Fugaz One', 'Glegoo', 'Gloria Hallelujah', 'Gochi Hand', 'Great Vibes', 'Homemade Apple', 'IBM Plex Serif', 'Inconsolata',
            'Indie Flower', 'Italianno', 'Josefin Sans', 'Julius Sans One', 'Kalam', 'Karma', 'Kaushan Script', 'Lato', 'Libre Baskerville', 'Lobster',
            'Luckiest Guy', 'Marcellus', 'Marck Script', 'Merienda', 'Merienda One', 'Mitr', 'Monda', 'Monoton', 'Montserrat', 'Montserrat Subrayada',
            'Mountains of Christmas', 'Nanum Gothic Coding', 'Nunito', 'Old Standard TT', 'Open Sans', 'Orbitron', 'Oswald', 'Pacifico', 'Pangolin', 'Parisienne',
            'Patrick Hand', 'Patua One', 'Permanent Marker', 'Pinyon Script', 'Play', 'Playfair Display', 'Playfair Display SC', 'Poiret One', 'Pontano Sans',
            'Poppins', 'Press Start 2P', 'Pridi', 'Quantico', 'Raleway', 'Righteous', 'Roboto', 'Roboto Slab', 'Rochester', 'Rock Salt', 'Rubik', 'Rubik Mono One',
            'Russo One', 'Sacramento', 'Sanchez', 'Satisfy', 'Sawarabi Mincho', 'Shadows Into Light', 'Shadows Into Light Two', 'Sigmar One', 'Slabo 27px',
            'Source Code Pro', 'Special Elite', 'Staatliches', 'Syncopate', 'Taviraj', 'Teko', 'Ultra', 'Unica One', 'Varela Round', 'Vidaloka', 'Zilla Slab'
        ]
        self.LocalFonts = ["Arial", "Verdana", "Helvetica", "Georgia"]

    def reconstruct_data(self, json_data):
        if isinstance(json_data, dict):
            self.store = json_data.get('platform_account', '')
            self.site = json_data.get('platform_account_site', '')
            self.sku = json_data.get('seller_sku')
            self.json_data = json_data.get('content', {})
        elif isinstance(json_data, list):
            self.json_data = json_data
        else:
            self.err = "json_data 必须是字典或列表"
            print(self.err)
            raise Exception(self.err)
        return {
            "identifier": generate_uuid(),
            "type": "PageContainerComponent",
            "templateIdentifier": self.template_identifier_map["ContainerPickerTemplate"],
            "children": [self.create_preview_container(item, index) for index, item in enumerate(self.json_data)]
        }

    @decorators
    def create_preview_container(self, item, index):
        preview_container = {
            "identifier": generate_uuid(),
            "label": item["label"],
            "type": "PreviewContainerComponent",
            "templateIdentifier": self.template_identifier_map["choices.content.PreviewContainerTemplate"],
            "children": [self.build_flat_container(item)],
            "name": f'表面 {index + 1}',
            "baseImage": {
                "imageUrl": item["baseImage.imageUrl"],
                "dimension": {
                    "width": 1200,
                    "height": 1200
                }
            },
            "maskImage": {
                "imageUrl": item["maskImage.imageUrl"],
                "dimension": {
                    "width": 1200,
                    "height": 1200
                }
            },
            "instructions": item["instructions"] or '',
        }
        return preview_container

    def build_flat_container(self, item):
        flat_container = {
            "identifier": generate_uuid(),
            "type": "FlatContainerComponent",
            "templateIdentifier": self.template_identifier_map["choices.content.children.ContainerPickerTemplate"],
            "children": [self.build_component(content) for content in item["content"]]
        }
        return flat_container

    def build_component(self, content):
        if content["type"] == "image":
            return self.build_image_component(content)
        elif content["type"] == "text":
            return self.build_text_component(content)
        elif content["type"] == "select":
            return self.build_option_chooser_component(content)
        else:
            self.err = f"[{self.store}] [{self.site}] [{self.sku}].未定义组件类型: {content['type']}"
            print(self.err)
            raise Exception(self.err)

    @decorators
    def build_image_component(self, component):
        count = self.increment_counter('image')
        placement_container = {
            "identifier": generate_uuid(),
            "type": "PlacementContainerComponent",
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.PlacementContainerTemplate"],
            "children": [
                {
                    "identifier": generate_uuid(),
                    "type": "ImageInputComponent",
                    "name": "",
                    "label": component["label"],
                    "instructions": component["instructions"] or '',
                    "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.ImageInputTemplate"],
                    "isRequired": parse_bool(component["isRequired"]),
                }
            ],
            "dimension": {
                "width": safe_int(component["dimension.width"]),
                "height": safe_int(component["dimension.height"])
            },
            "position": {
                "x": safe_int(component["position.x"]),
                "y": safe_int(component["position.y"])
            },
            "isFreePlacement": True,
            "label": f"图片 {count}",
            "name": f"图片 {count}"
        }
        return placement_container

    def increment_counter(self, component_type):
        """递增特定组件类型的计数器并返回计数值"""
        if component_type not in self.type_counter:
            self.type_counter[component_type] = 0
        self.type_counter[component_type] += 1
        return self.type_counter[component_type]

    @decorators
    def build_text_component(self, component):
        font_chooser = self.create_font_chooser(component)
        color_chooser = self.create_color_chooser(component)
        placement_containers = self.create_text_input_components(component)

        inner_container = {
            "identifier": generate_uuid(),
            "type": "ContainerComponent",
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.ContainerPickerTemplate"],
            "children": placement_containers
        }

        count = self.increment_counter('text')
        outer_container = {
            "identifier": generate_uuid(),
            "type": "ContainerComponent",
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.ContainerTemplate"],
            "children": [font_chooser, color_chooser, inner_container],
            "label": f"文本 {count}",
            "name": f"文本 {count}"
        }
        return outer_container

    @decorators
    def create_font_chooser(self, component):
        font_options = []
        for font_family in component["family"]["family"]:
            if font_family in self.GoogleFonts:
                fontType = "GoogleFont"
            elif font_family in self.LocalFonts:
                fontType = "LocalFont"
            else:
                fontType = "LocalFont"
                self.err = f"[{font_family}] 未识别字体，已默认为LocalFont类型字体，如发布失败，请联系管理员！"
                print(self.err)
            font_options.append({
                "identifier": generate_uuid(),
                "family": font_family,
                "fontType": fontType,
            })
        return {
            "identifier": generate_uuid(),
            "type": "FontChooserComponent",
            "name": "",
            "label": component["family"]["label"],
            "instructions": component["family"]["instructions"] or '',
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.FontChooserTemplate"],
            "defaultFontIdentifier": font_options[0]["identifier"],
            "fontOptions": font_options,
            "isRequired": False,
        }

    @decorators
    def create_color_chooser(self, component):
        color_options = [
            {
                "identifier": generate_uuid(),
                "name": color["name"],
                "value": color["value"],
                "colorModel": "rgb"
            } for color in component["color"]["option"]
        ]
        return {
            "identifier": generate_uuid(),
            "type": "ColorChooserComponent",
            "name": "",
            "label": component["color"]["label"],
            "instructions": component["color"]["instructions"] or '',
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.ColorChooserTemplate"],
            "colorOptions": color_options,
            "defaultColorIdentifier": color_options[0]["identifier"],
        }

    @decorators
    def create_text_input_components(self, component):
        placement_containers = []
        for text_item in component['text']:
            placement_container = {
                "identifier": generate_uuid(),
                "type": "PlacementContainerComponent",
                "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.choices.content.PlacementContainerTemplate"],
                "children": [{
                    "identifier": generate_uuid(),
                    "type": "FlatRatePriceDeltaContainerComponent",
                    "templateIdentifier": self.template_identifier_map[
                        "choices.content.children.choices.content.children.choices.content.children.FlatRatePriceDeltaContainerTemplate"],
                    "priceDelta": {
                        "amount": text_item.get("amount", 0)
                    },
                    "children": [{
                        "identifier": generate_uuid(),
                        "type": "TextInputComponent",
                        "name": "",
                        "label": text_item["label"],
                        "instructions": text_item["instructions"] or '',
                        "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.children.choices.content.children.children.TextInputTemplate"],
                        "minLength": safe_int(text_item["minLength"]),
                        "maxLength": safe_int(text_item["maxLength"]),
                        "regexChoice": self.allowed_characters.get(text_item["regexChoice"], "default-id"),
                        "isRequired": parse_bool(text_item["isRequired"]),
                        "placeholder": text_item["placeholder"] or '',
                        "maxLines": safe_int(text_item["maxLines"]),
                    }]
                }],
                "dimension": {
                    "width": safe_int(text_item["dimension.width"]),
                    "height": safe_int(text_item["dimension.height"])
                },
                "position": {
                    "x": safe_int(text_item["position.x"]),
                    "y": safe_int(text_item["position.y"])
                },
                "isFreePlacement": parse_bool(text_item["isFreePlacement"]),
                "label": f"文本输入 {len(placement_containers) + 1}",
                "name": f"文本输入 {len(placement_containers) + 1}"
            }

            placement_containers.append(placement_container)
        return placement_containers

    @decorators
    def build_option_chooser_component(self, component):
        options = [
            {
                "identifier": generate_uuid(),
                "name": option["label"],
                "label": option["label"],
                "additionalCost": {
                    "amount": safe_int(option["additionalCost.amount"])
                },
                **({"thumbnailImage": {
                    "imageUrl": option["thumbnailImage.imageUrl"],
                    "dimension": {
                        "width": 1000,
                        "height": 1000
                    }
                }} if "thumbnailImage.imageUrl" in option else {}),
                **({"overlayImage": {
                    "imageUrl": option["overlayImage.imageUrl"],
                    "dimension": {
                        "width": 1600,
                        "height": 1600
                    }
                }} if "overlayImage.imageUrl" in option else {})
            } for option in component["options"]
        ]

        option_chooser = {
            "identifier": generate_uuid(),
            "type": "OptionChooserComponent",
            "name": f"选项下拉列表 {self.increment_counter('select')}",
            "label": component["label"],
            "instructions": component["instructions"] or '',
            "templateIdentifier": self.template_identifier_map["choices.content.children.choices.content.OptionChooserTemplate"],
            "options": options,
            "isRequired": parse_bool(component.get("isRequired", False))
        }
        return option_chooser


def filter_result_data(data):
    if isinstance(data, dict):
        keys_to_remove = [key for key, value in data.items() if key in ['dimension', 'position', 'minResolution'] and all(v < 0 for v in value.values())]
        for key in keys_to_remove:
            del data[key]
        for key, value in data.items():
            data[key] = filter_result_data(value)
    elif isinstance(data, list):
        for i in range(len(data)):
            data[i] = filter_result_data(data[i])
    return data


def get_template_identifier(data: dict) -> dict:
    result = {}

    def recursive_search(sub_data, path=''):
        if isinstance(sub_data, dict):
            if 'type' in sub_data and 'Template' in sub_data['type']:
                key = f"{path}.{sub_data['type']}" if path else sub_data['type']
                result[key] = sub_data['identifier']
            for k, v in sub_data.items():
                recursive_search(v, f"{path}.{k}" if path else k)
        elif isinstance(sub_data, list):
            for item in sub_data:
                recursive_search(item, path)

    recursive_search(data)
    return result


def generate_uuid():
    return str(uuid.uuid4())


def safe_int(value, default=0):
    try:
        return int(value)
    except ValueError:
        return default


def parse_bool(value):
    """
    解析各种格式的布尔值输入
    支持: 0/1, "0"/"1", True/False, "true"/"false", "True"/"False"
    """
    if isinstance(value, bool):
        return value
    if isinstance(value, (int, float)):
        return bool(value)
    if isinstance(value, str):
        value = value.lower().strip()
        if value in ('true', '1'):
            return True
        if value in ('false', '0'):
            return False
    return False


def main():
    # 示例 JSON 数据
    json_data = [
        {
            'seller_sku': 'DYX01-Kfmdzt-11-Customize',
            'label': 'pour Apple iPhone 11',
            'instructions': 'Personalisierung: 1.Bild + Text. 2.nur Bild. 3.nur Text.',
            'baseImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/AVIS3IHGX16G8/dbcf414f-ccec-4ce4-8131-5896936402c3.jpg',
            'maskImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/AVIS3IHGX16G8/4efd596a-6f66-4578-9db3-119f5e690490.png',
            'content': [
                {
                    'type': 'image',
                    'label': 'Bild hochlade',
                    'instructions': 'Wählen Sie Ihr hochauflösendes Bild zum Hochladen!',
                    'isRequired': '0',
                    'dimension.width': '239',
                    'dimension.height': '400',
                    'position.x': '161',
                    'position.y': '0',
                },
                {
                    'type': 'select',
                    'label': 'Bordure de motif',
                    'instructions': 'Choisissez votre motif de bordure préféré',
                    'isRequired': '1',
                    'options': [
                        {
                            'label': 'BK9',
                            'overlayImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A13V1IB3VIYZZH/A3VR5H7BCB4SE/e697ef5c-9dec-43ef-8d10-6a812668ac9e.png',
                            'thumbnailImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A13V1IB3VIYZZH/A3VR5H7BCB4SE/f7750c8d-e36b-4781-9a88-0d122fcd4bb3.jpg',
                            'additionalCost.amount': '0',
                        }, {
                            'label': 'BK10',
                            'overlayImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A13V1IB3VIYZZH/A3VR5H7BCB4SE/c6217a08-d6f6-4388-b95d-b24e6a9bc6c1.png',
                            'thumbnailImage.imageUrl': 'https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A13V1IB3VIYZZH/A3VR5H7BCB4SE/61275b6d-18e1-41a7-bc3c-f53c706a6205.jpg',
                            'additionalCost.amount': '0',
                        },
                    ],
                },
                {
                    'type': 'text',
                    'family': {
                        'label': 'Schriftart',
                        'instructions': 'Wählen Sie den gewünschten',
                        'family': [
                            "Abril Fatface",
                            "Acme",
                            "Alfa Slab One",
                            "Anton",
                            "Archivo Black",
                            "Audiowide",
                            "Arvo",
                            "Baloo",
                            "Bree Serif",
                            "Chewy",
                            "Days One",
                            "Fjalla One",
                            "Fredoka One",
                            "Fugaz One",
                            "Georgia",
                            "Gochi Hand",
                            "Helvetica",
                            "Kalam",
                            "Mitr",
                            "Permanent Marker",
                        ],
                    },
                    'color': {
                        'label': 'Farbe des Texte',
                        'instructions': 'Wählen Sie Ihre bevorzugte',
                        'option': [
                            {
                                'name': '黑色',
                                'value': '#000000',
                            },
                            {
                                'name': 'Weiß',
                                'value': '#ffffff',
                            },
                            {
                                'name': 'Orange',
                                'value': '#ff914d',
                            }
                        ],
                    },
                    'text': [
                        {
                            'label': 'Text eingeben',
                            'instructions': 'Geben Sie den Text ein, den Sie auf Ihrer Handyhülle hinterlassen möchten',
                            'minLength': '1',
                            'maxLength': '1000',
                            'placeholder': 'hi',
                            'isRequired': '0',
                            'regexChoice': '不包括表情符号的所有字符',
                            'maxLines': '3',
                            'isFreePlacement': '1',
                            'dimension.width': '164',
                            'dimension.height': '325',
                            'position.x': '213',
                            'position.y': '26',
                        },
                        {
                            'label': 'Text eingeben2',
                            'instructions': 'Geben Sie den Text ein, den Sie auf Ihrer Handyhülle hinterlassen möchten2',
                            'minLength': '3',
                            'maxLength': '1000',
                            'placeholder': 'hi2',
                            'isRequired': '0',
                            'regexChoice': '任何字母或数字（仅英文）',
                            'maxLines': '2',
                            'isFreePlacement': '1',
                            'dimension.width': '163',
                            'dimension.height': '324',
                            'position.x': '212',
                            'position.y': '22',
                        },
                    ],
                },
            ]
        }
    ]
    json_data = {
        "platform_account": "AM_WBK-FR",
        "platform_account_site": "FR",
        "seller_sku": "WBK05-F-SLDZCZBS",
        "content": [
            {
                "seller_sku": "",
                "label": "Personalisiertes Armband mit Foto",
                "baseImage.imageUrl": "https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/A7B5XS8PERL3F/9194339848b23fa691e0981eebddbdd0.png",
                "maskImage.imageUrl": "https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/A7B5XS8PERL3F/9194339848b23fa691e0981eebddbdd0.png",
                "content": [
                    {
                        "type": "image",
                        "label": "type 5",
                        "instructions": "",
                        "isRequired": "1",
                        "dimension.width": "400",
                        "dimension.height": "400",
                        "position.x": "0",
                        "position.y": "0",
                        "overlayImage.imageUrl": "https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/A7B5XS8PERL3F/0198dd67af663233615d425da202444d.png",
                        "thumbnailImage.imageUrl": "https://m.media-amazon.com/images/S/gestalt-seller-images-prod-eu-west-1/A1PA6795UKMFR9/A7B5XS8PERL3F/37efb0c66071b6472a70a7561b5b7daf.jpg",
                        "additionalCost.amount": "0"
                    }
                ]
            }
        ]
    }
    reqbb = GoodDiyRequestBodyBuilder()
    reconstructed_request_body = reqbb.reconstruct_data(json_data)
    print(reconstructed_request_body)
    print(reqbb.err)


if __name__ == '__main__':
    main()
