# -*- coding:UTF-8 -*-
# @FileName  :紫鸟后台店铺商品定制
# @Time      :2024年11月11日14:01:53
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.紫鸟.ZiNiao import *

task_tb_name = 'bms.`amazon_task_diy`'


def main():
    main_tasks = MS.get_dict(
            f'SELECT * FROM {task_tb_name} where status in (0,1)'
            if not is_test_environment() else
            f'SELECT * FROM {task_tb_name} where id  in (19987)'
    )
    if not main_tasks:
        time.sleep(10)
        return
    for task in main_tasks:
        task_id = task.get('id')
        store_name = task.get('super_browser_name')

        # 修改所关联任务状态为执行中
        if MS.update(f'UPDATE {task_tb_name} SET status = 2,`plan_time` = %s WHERE id = %s and `status` in (0,1)', (now_int(), task_id)) < 1:
            logging(f'当前任务已经在执行')
            if not is_test_environment():
                return False

        task_params = {  # 更新任务表/数据表记录的字段
            'task_id': task_id,
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'task_tb_name': task_tb_name,
        }
        task_params.update(task)
        manager.task_params.update(task_params)
        logging(f'当前应用任务ID:{task_id}')

        if manager.task_params.get('cur_store_name') != store_name or not manager.task_params.get('cur_store_info'):
            # 开始抓取任务
            browser_list = manager.get_browser_list()
            store = [x for x in browser_list if store_name == x['browserName']]
            store = store[0] if store else None
            if browser_list is None or not store:
                manager.task_params['result'] = f'紫鸟账户【{manager.user_info["username"]}】 获取店铺{store_name}信息失败'
                logging(manager.task_params['result'])
                manager.task_params['finally_result'] += manager.task_params['result']
                manager.task_params['finally_status'] = 20
                # fsmsg.send(None, manager.task_params['result'])
            else:
                manager.task_params['cur_store_name'] = store_name
                manager.task_params['cur_store_info'] = {}
                manager.task_goods_diy(store)
        else:
            store = manager.task_params.get('cur_store_info')
            if manager.task_params.get('cur_store_name') != store_name:
                manager.task_params['cur_store_name'] = store_name
                manager.task_params['cur_store_info'] = {}
            elif not manager.task_params.get('cur_store_name'):
                manager.task_params['cur_store_name'] = store_name
            manager.task_goods_diy(store)

        status = max(manager.task_params.get('finally_status'), manager.task_params.get('status'))
        # 更新任务状态可以修改
        MS.update(
                f'update {task_tb_name} set status = %s, response_message = %s, done_time=%s where id = %s',
                (status, manager.task_params.get('finally_result'), now_int(), task_id)
        )

        logging(f'任务:{task_id} 已执行结束')
        print()


if __name__ == "__main__":
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    manager = StoreManager(max_workers=1)

    while True:
        try:
            main()
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = f'{get_cur_run_file()}主程序异常！'
            logging(msg)
            fsmsg.send(None, msg, err)
            manager = StoreManager(max_workers=1)
            manager.reboot()
