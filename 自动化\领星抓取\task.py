import time
import requests
import pickle
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import logging
import base64
import uuid
import json
import datetime
import msgLog
import ReqServe
from datetime import datetime, timedelta

logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
)
SESSION_FILE = "session.pkl"  # 保存 session 的文件名


class SessionManager:
    """管理会话的类"""

    def __init__(self):
        self.session = None

    def save_session(self, session):
        """保存 session 对象到文件"""
        with open(SESSION_FILE, "wb") as f:
            pickle.dump(session, f)
        logging.info("Session 已保存到文件")

    def load_session(self):
        """从文件加载 session 对象"""
        if os.path.exists(SESSION_FILE):
            with open(SESSION_FILE, "rb") as f:
                self.session = pickle.load(f)
            logging.info("Session 已从文件加载")

            return True
        else:
            logging.info("未找到保存的 Session 文件")
            return False

    def create_new_session(self):
        """创建一个新的 session 对象"""
        self.session = requests.Session()

    def get_session(self):
        """获取 session 对象，若不存在则创建"""
        if not self.session:
            self.load_session()
            if not self.session:
                self.create_new_session()
        return self.session

    def check_cookie(self):
        """检测并修正 Cookie 值的类型"""
        for cookie in self.session.cookies:
            # 检测值的类型，如果不是字符串或字节，则转换为字符串
            if not isinstance(cookie.value, (str, bytes)):
                # 将值转换为字符串
                corrected_value = str(cookie.value)
                self.session.cookies.set(
                        cookie.name, corrected_value,
                        domain=cookie.domain, path=cookie.path,
                        expires=cookie.expires)
                logging.warning(
                        f"Cookie 值类型错误，已修正: {cookie.name} = {corrected_value}")

    logging.info("Cookie 检查完成，所有值均为字符串类型")

    def set_cookie(self, name, value, domain="muke.lingxing.com", path="/", expires=0):
        """设置 Cookie"""
        if expires != 0:
            # 获取当前时间并加上指定的天数 (默认 30 天)
            expires_time = datetime.datetime.utcnow() + datetime.timedelta(days=expires)

            # 将过期时间转换为时间戳（秒数）
            expires = int(expires_time.timestamp())
        self.session.cookies.set(name, value, domain=domain,
                                 path=path, expires=expires)
        logging.info(f"Cookie 设置: {name} = {str(value)}")

    def remove_cookie(self, name):
        """移除指定的 Cookie，若存在"""
        if name in self.session.cookies:
            self.session.cookies.clear(name)
            logging.info(f"Cookie 移除: {name}")
        else:
            logging.info(f"Cookie {name} 不存在，未执行移除操作")


class CommonClass:
    """公用工具类"""

    def __init__(self):
        pass

    def getuuid(self):
        """生成 UUID"""
        return str(uuid.uuid4())

    def encrypt(self, F, q):
        """加密方法"""
        if isinstance(F, str):  # 如果密钥是字符串，转换为字节
            F = F.encode('utf-8')

        # 将字符串 q 转换为字节
        l = q.encode('utf-8')

        # 创建 AES 加密对象，ECB 模式，不需要 IV
        cipher = AES.new(F, AES.MODE_ECB)

        # Pkcs7 填充
        encrypted = cipher.encrypt(pad(l, AES.block_size))

        # 转换为 Base64 字符串
        return base64.b64encode(encrypted).decode('utf-8')

    def get_last_three_months_range(self):
        """
        获取最近三个月的日期范围，从当前日期往前推。
        :return: (start_date, end_date) 起始日期和结束日期
        """
        today = datetime.today()
        # 计算三个月前的日期
        three_months_ago = today - timedelta(days=90)  # 粗略三个月为90天
        # 格式化日期
        start_date = three_months_ago.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        return start_date, end_date


class Login:
    """处理登录操作的类"""

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.session = self.session_manager.get_session()
        self.sensorsAnonymousId = "1932f022962709-09f55794af05688-********-2073600-1932f0229631113"

    def login(self, account, pwd, uuid, x_ak_request_id, secretId):
        """登录请求"""
        url = "https://gw.lingxingerp.com/newadmin/api/passport/login"

        payload = {
            "account": account,
            "pwd": pwd,
            "verify_code": "",
            "uuid": uuid,
            "auto_login": 1,
            "sensorsAnonymousId": self.sensorsAnonymousId,
            "secretId": secretId,
            "doubleCheckLoginReq": {
                "doubleCheckType": 1,
                "mobileLoginCode": "",
                "loginTick": ""
            }
        }

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': '',
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
            'x-ak-env-key': 'muke',
            'x-ak-request-id': x_ak_request_id,
            'x-ak-request-source': 'erp',
            'x-ak-version': 'AKVERSIONNUM',
            'x-ak-zid': '',
            'content-type': 'application/json;charset=UTF-8',
            'Host': 'gw.lingxingerp.com',
            'Connection': 'keep-alive'
        }

        response = self.session.post(
                url, data=json.dumps(payload), headers=headers)
        print(response.json())
        if response.status_code == 200 and response.json().get('code') == 1:
            print(response.json())

            # 删除特定的 cookies
            self.session_manager.remove_cookie("isCooperativeAccountLogin")
            self.session_manager.remove_cookie("cooperativeCustomer")
            self.session_manager.remove_cookie("coordinatedUsername")
            self.session_manager.remove_cookie("cooperativeAccount")
            self.session_manager.remove_cookie("cooperativeAccountUniqueKey")
            self.session_manager.remove_cookie("tokenExpireTime")

            # 设置新的 cookies
            e = response.json()
            self.session_manager.set_cookie(
                    "company_id", e.get("companyId"), expires=30)
            self.session_manager.set_cookie(
                    "envKey", e.get("envKey"), expires=30)
            self.session_manager.set_cookie(
                    "env_key", e.get("envKey"), expires=30)
            self.session_manager.set_cookie(
                    "auth-token", e.get("token"), expires=30)
            # self.session_manager.set_cookie(
            #     "authToken", e.get("token"), expires=30)

            self.session_manager.set_cookie("uid", e.get("uid"), expires=30)
            self.session_manager.set_cookie(
                    "isNeedReset", str(e.get("needReset")), expires=30)
            if e.get("sellerAuthErpUrl"):
                self.session_manager.set_cookie(
                        "seller-auth-erp-url", e.get("sellerAuthErpUrl"), expires=30, domain="lingxing.com")
            is_pwd_notice = "1" if e.get("isPwdNotice") else "0"
            self.session_manager.set_cookie(
                    "isUpdatePwd", is_pwd_notice, expires=30)
            self.session_manager.set_cookie(
                    "zid", e.get("zid"), expires=30)  # 30 days
            self.session_manager.set_cookie("isLogin", "true", expires=0)
            # self.session_manager.check_cookie()
            # 登录成功后保存 session
            self.session_manager.save_session(self.session)
        else:
            logging.error(f"登录失败: {response.text}")
        return response.json()

    def getLoginSecretKey(self, uuid):
        """获取登录密钥"""
        url = "https://gw.lingxingerp.com/newadmin/api/passport/getLoginSecretKey"

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': '',
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
            'x-ak-env-key': 'muke',
            'x-ak-request-id': uuid,
            'x-ak-request-source': 'erp',
            'x-ak-version': 'AKVERSIONNUM',
            'x-ak-zid': '',
            'Host': 'gw.lingxingerp.com',
            'Connection': 'keep-alive'
        }

        response = self.session.post(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            logging.error(f"获取登录密钥失败: {response.text}")
            return None


class Task:
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.req_session = self.session_manager.get_session()
        self.proxies = {
            "http": "http://127.0.0.1:8888",
            "https": "http://127.0.0.1:8888",
        }

    # def get_all_cookies(self):
    #     """从 session 中获取所有的 cookies"""
    #     # 获取所有 cookies
    #     all_cookies = self.session.cookies.get_dict()

    #     if all_cookies:
    #         logging.info("所有 Cookie 获取成功:")
    #         for name, value in all_cookies.items():
    #             logging.info(f"{name} = {value}")
    #         return all_cookies
    #     else:
    #         logging.warning("没有找到任何 Cookies")
    #         return None

    def get_cookie(self, name):
        """从 session 中获取指定的 cookie"""
        # 检查 cookie 是否存在
        cookie_value = self.req_session.cookies.get(name)

        if cookie_value:
            logging.info(f"Cookie 获取成功: {name} = {cookie_value}")
            return cookie_value
        else:
            logging.warning(f"Cookie {name} 不存在")
            return None

    # 装箱任务-列表
    def PackingTaskList(self, req_uuid, offset, length, ):
        try:
            url = "https://muke.lingxing.com/api/module/packingTaskBox/PackingTask/list"

            token = self.get_cookie("auth-token")
            uid = self.get_cookie("uid")
            company_id = self.get_cookie('company_id')

            # # 将 payload 转换为字典
            payload = {
                "search_field_time": "create",
                "search_field": "relate_sn",
                "search_value": "",
                "offset": offset,
                "length": length,
                "senior_search_list": [],
                "req_time_sequence": "/api/module/packingTaskBox/PackingTask/list$$1"
            }
            proxies = {
                "http": "http://127.0.0.1:8888",
                "https": "http://127.0.0.1:8888",
            }

            # 请求头
            self.req_session.headers.update({
                'AK-Client-Type': 'web',
                'AK-Origin': 'https://muke.lingxing.com',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Connection': 'keep-alive',
                'Origin': 'https://muke.lingxing.com',
                'Referer': 'https://muke.lingxing.com/erp/msupply/packBoxStack',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
                'X-AK-Company-Id': f'{company_id}',
                'X-AK-ENV-KEY': 'muke',
                'X-AK-PLATFORM': '1',
                'X-AK-Request-Id': f'{req_uuid}',
                'X-AK-Request-Source': 'erp',
                'X-AK-Uid': f'{uid}',
                'X-AK-Version': '*******.0.042',
                'X-AK-Zid': '1',
                'auth-token': token,
                'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Content-Type': 'application/json;charset=UTF-8',
                'Host': 'muke.lingxing.com'
            })
            response = self.req_session.post(
                    url, json=payload, proxies=proxies, verify=False)
            # response = self.req_session.post(
            #     url, json=payload)

            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求过程中发生错误: {str(e)}")
            return None

    # 装箱任务-详细
    def gettaskDetail(self, req_uuid, pt_id):
        try:
            url = f"https://muke.lingxing.com/api/module/packingTaskBox/PackingTask/taskDetail?pt_id={pt_id}&req_time_sequence=%2Fapi%2Fmodule%2FpackingTaskBox%2FPackingTask%2FtaskDetail$$5"

            token = self.get_cookie("auth-token")
            uid = self.get_cookie("uid")
            company_id = self.get_cookie('company_id')

            # 请求头
            self.req_session.headers.update({
                'AK-Client-Type': 'web',
                'AK-Origin': 'https://muke.lingxing.com',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Connection': 'keep-alive',
                'Referer': 'https://muke.lingxing.com/erp/msupply/packBoxStack',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
                'X-AK-Company-Id': f'{company_id}',
                'X-AK-ENV-KEY': 'muke',
                'X-AK-PLATFORM': '1',
                'X-AK-Request-Id': f'{req_uuid}',
                'X-AK-Request-Source': 'erp',
                'X-AK-Uid': f'{uid}',
                'X-AK-Version': '*******.0.042',
                'X-AK-Zid': '1',
                'auth-token': f'{token}',
                'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Host': 'muke.lingxing.com'
            })
            proxies = {
                "http": "http://127.0.0.1:8888",
                "https": "http://127.0.0.1:8888",
            }
            # response = self.req_session.post(
            #     url, json=payload, proxies=proxies,verify=False)
            response = self.req_session.get(
                    url, proxies=proxies, verify=False)

            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求过程中发生错误: {str(e)}")
            return None

    def getPlanGroupList(self, req_uuid, offset, length, start_date, end_date):
        try:
            url = "https://muke.lingxing.com/api/fba_plan/planGroupList"
            token = self.get_cookie("auth-token")
            uid = self.get_cookie("uid")
            company_id = self.get_cookie('company_id')

            payload = {
                "receive_warehouse_type": "1",
                "status": "",
                "packing_type": "",
                "create_uids": [],
                "audit_uids": [],
                "temp_wid": [],
                "is_relate_list": "",
                "is_relate_mws": "",
                "is_relate_purchase": "",
                "is_relate_process": "",
                "spo_status": [],
                "is_relate_overseas": "",
                "is_relate_packing_task_sn": "",
                "lock_status": "",
                "shipment_status": [],
                "ship_mode": "",
                "is_urgent": "",
                "print_status": "",
                "sids": "",
                "mids": "",
                "search_field_time": "gmt_create",
                "start_date": start_date,
                "end_date": end_date,
                "search_field": "seq",
                "search_value": "",
                "seniorSearchList": [],
                "offset": offset,
                "length": length,
                "req_time_sequence": "/api/fba_plan/planGroupList$$3"
            }
            # 请求头
            self.req_session.headers.update({
                'AK-Client-Type': 'web',
                'AK-Origin': 'https://muke.lingxing.com',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Connection': 'keep-alive',
                'Origin': 'https://muke.lingxing.com',
                'Referer': 'https://muke.lingxing.com/erp/msupply/shipmentPlan',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
                'X-AK-Company-Id': company_id,
                'X-AK-ENV-KEY': 'muke',
                'X-AK-PLATFORM': '1',
                'X-AK-Request-Id': req_uuid,
                'X-AK-Request-Source': 'erp',
                'X-AK-Uid': uid,
                'X-AK-Version': '*******.0.012',
                'X-AK-Zid': '1',
                'auth-token': token,
                'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Content-Type': 'application/json;charset=UTF-8',
                'Host': 'muke.lingxing.com'
            })
            response = self.req_session.post(
                    url, json=payload, proxies=self.proxies, verify=False)
            # response = self.req_session.post(
            #     url, json=payload)

            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求过程中发生错误: {str(e)}")
            return None


if __name__ == "__main__":
    try:
        # 初始化工具和会话管理
        session_manager = SessionManager()
        common_obj = CommonClass()
        login_task = Login(session_manager)
        session = session_manager.load_session()
        if session:
            # 获取装箱数据
            task_run = Task(session_manager)
            req_uuid = common_obj.getuuid()
            # getPackingTaskList = task_run.PackingTaskList(
            #     req_uuid=req_uuid, offset=0, length=100)
            # if getPackingTaskList.get('code') == 1:
            #     hadReqNum = 100
            #     hadOffset = 0
            #     ReqList = []
            #     ReqList = getPackingTaskList.get(
            #         'data', {}).get("list", []) + ReqList
            #     while True:
            #         hadOffset += 1
            #         if getPackingTaskList.get('data', {}).get("total", 0) > hadReqNum:
            #             nextPackingTaskList = task_run.PackingTaskList(
            #                 req_uuid=req_uuid, offset=hadOffset, length=100)
            #             if nextPackingTaskList.get('code') == 1:
            #                 ReqList = nextPackingTaskList.get(
            #                     'data', {}).get("list", []) + ReqList
            #             hadReqNum += 100
            #         else:
            #             break
            #     # logging.info(ReqList)
            #     time.sleep(1)
            #     for item_id in ReqList:
            #         gettaskDetail = task_run.gettaskDetail(
            #             req_uuid=common_obj.getuuid(), pt_id=item_id.get('pt_id'))
            #         logging.info(gettaskDetail)
            #         req_serve = ReqServe.ReqServe()
            #         req_serve.saveBox(params=gettaskDetail.get('data'))
            # 获取发货计划、
            start_date, end_date = common_obj.get_last_three_months_range()
            planGroupList = task_run.getPlanGroupList(req_uuid=common_obj.getuuid(), offset=0, length=100, start_date=start_date, end_date=end_date)
            if planGroupList.get('code') == 1:
                hadReqNum = 100
                hadOffset = 0
                req_serve = ReqServe.ReqServe()
                req_serve.saveFbaPlan(params={"plan_list": planGroupList.get("data").get("plan_list")})
                while True:
                    hadOffset += 1
                    if planGroupList.get('data', {}).get("total", 0) > hadReqNum:
                        nextPackingTaskList = task_run.getPlanGroupList(req_uuid=common_obj.getuuid(), offset=hadOffset, length=100, start_date=start_date, end_date=end_date)
                        if nextPackingTaskList.get('code') == 1:
                            req_serve = ReqServe.ReqServe()
                            req_serve.saveFbaPlan(params={"plan_list": nextPackingTaskList.get("data").get("plan_list")})
                        hadReqNum += 100
                    else:
                        break
            # 获取发货单

        else:
            # 第一步：获取登录密钥
            get_uuid = common_obj.getuuid()
            secret_key_response = login_task.getLoginSecretKey(get_uuid)
            if secret_key_response and secret_key_response.get("code") == 1:
                secret_key = secret_key_response["data"]["secretKey"]
                secret_id = secret_key_response["data"]["secretId"]
                logging.info(f"登录密钥信息获取成功==>{secret_id} {secret_key}")

                # 第二步：登录
                account = "jszg01"
                password = "guornjszh1"
                encrypt_pwd = common_obj.encrypt(F=secret_key, q=password)
                x_ak_request_id = common_obj.getuuid()

                login_response = login_task.login(
                        account, encrypt_pwd, get_uuid, x_ak_request_id, secret_id)
                logging.info(login_response)
    except Exception as e:
        logging.info(e)
