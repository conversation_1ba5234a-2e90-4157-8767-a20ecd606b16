import json
import requests
from urllib.parse import quote
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import *
from utils_mrc.SpiderTools import IPPool
from utils_mrc.RedisClient import *
import random

conn = RedisClient('cookies', 'spriteEp')


class SellerSpriteExpansion:
    def __init__(self, token='', user='', pwd=''):
        # self.session = requests.Session()  # 创建Session对象
        self.session = IPPool.session2
        self.session.headers.update({
            'accept': 'application/json',
            'content-type': 'application/json',
            'Host': 'www.sellersprite.com',
            'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            'random-token': generate_uuid(),
            'Connection': 'keep-alive',
            'accept-language': 'zh-CN,zh;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6',
        })
        # 基本都是固定值，插件更新会有变动，这里是4.5.0的插件固定值
        # 插件id
        self.extension = "libkfdihmladjiijocodhhkkkbjiadpd"
        self.extension = "ecanjpklimgeijdcdpdfoooofephbbln"
        # 当前语言
        self.language = "zh_CN"
        # 插件版本号
        self.version = "4.5.0"
        self.version = "4.6.1"
        # 基础列表页一级路由
        self.host = "https://www.sellersprite.com/v2/extension/competitor-lookup/"
        self.source = "offline"
        self.user = user
        self.pwd = pwd
        self.params_token = token
        self.token = '' if is_mrc_environment() else conn.random()

    def login(self, email, password):
        password = calculate_md5_hash(password)

        TokenGenerator.init()
        tk = TokenGenerator.seller_sprite_token(self.user, password, None, None)

        url = f"https://www.sellersprite.com/v2/extension/signin?email={email}&password={password}&tk={tk}&version={self.version}&language={self.language}&extension={self.extension}&source={self.source}"

        response = self.session.get(url)
        result = response.json()
        if result.get('code') != "OK" or not result.get('data').get('token'):
            fsmsg.send(None, f"{self.user} 卖家精灵插件登陆失败！请确认账号是否异常！")
            raise Exception(f"{self.user} 登陆失败！")
        self.save_login_info(result)
        return result

    def save_login_info(self, result):
        self.token = self.params_token = result['data']['token']
        print(f'登陆成功！【{self.user}】token为：{self.token}')
        # json_str = json.dumps(result['data'])
        # sql = "INSERT INTO `rpa`.`rpa_info` (`source`, `info_data`, `remark`, `expired`) VALUES (%s, %s, %s, %s) ON DUPLICATE KEY UPDATE `info_data` = VALUES(`info_data`), `remark` = VALUES(`remark`)"
        # MS100.insert(sql, (f'sse_cookies_{self.user}', json_str, self.token, 0))
        # if MS100.err:
        #     print(f"保存登录信息失败：{MS100.err}")
        rpa_save_info(f'sse_cookies_{self.user}', result['data'])

    def get_login_info(self):
        if not self.user:
            sql = "select source FROM `rpa_info` where remark = %s"
            source = MS100.get_one(sql, (self.token,))
            if not source:
                raise Exception(f'获取{self.token}关联登录信息失败！')
            source = source[0]
            self.user = source.replace('sse_cookies_', '')

        sql = f"""
        SELECT info_data FROM `rpa_info`
        WHERE JSON_SEARCH(info_data, 'one', '{self.user}') IS NOT NULL and source like 'seller_sprite_user_cj%';
        """

        rs = MS100.get_one(sql)
        if not rs:
            raise Exception(f'获取{self.user}登录信息失败！')
        info_data = rs[0]
        info = json.loads(info_data)
        self.user = info['username']
        self.pwd = info['password']
        self.login(email=self.user, password=self.pwd)

    def get_list(self, country, asin_list):
        if not country or not asin_list:
            print('站点或asin不能为空！')
            return {'error': '站点或asin不能为空！'}
        asin_str = ",".join(asin_list) if len(asin_list) > 1 else asin_list[0]
        req_str = quote(asin_str, encoding='utf-8')
        tk = get_task_tk(asin_str=asin_str)  # 随入参变化而变化
        url = f"{self.host}quick-view/{country}?asins={req_str}&source=offline&miniMode=false&withRelation=true&withSaleTrend=false&tk={tk}&version={self.version}&language={self.language}&extension={self.extension}"
        response = self.req(url)
        try:
            data = response.json()
        except:
            data = response.text if response else ''
            print(data)
        return data

    def req(self, url):
        response = None
        for _ in range(3):
            try:
                self.token = self.params_token or conn.random()  # 优先使用传入的token,否则使用随机token
                self.session.headers['auth-token'] = self.token
                # if is_test_environment(): logging(f'当前使用token为：{self.token}')
                response = self.session.get(url)

                rs_data = response.json().get('data') if 'application/json' in str(response.headers.get('content-type')).lower() else ''
                if '令牌过期' in response.text or not response.text or not rs_data:
                    self.params_token = ''
                    # MS100.update(f"update rpa.`rpa_info` set `expired` = 1 where `remark` = %s", (self.token,))
                    # # if rs > 0:  # 如果使用cookies池，则取关联账号重新登录更新令牌
                    # #     self.get_login_info()
                    # #     raise Exception(response.text)
                    # # elif self.get_and_set_random_token():  # 否则尝试获取一个有效令牌
                    # #     raise Exception(response.text)
                    # logging(f'卖家精灵接口令牌{self.token}过期，即将重新获取！')
                    # self.get_login_info()
                    # raise Exception(response.text)
                    logging(f'卖家精灵接口令牌{self.token}过期，重新获取下一个！')
                    continue
                response.raise_for_status()
                return response
            except requests.exceptions.ProxyError:
                logging(f'代理异常')
            except:
                e = traceback.format_exc()
                logging(f'第{_ + 1}次请求失败：{e}')
                time.sleep(2)
        else:
            logging(f'卖家精灵接口请求失败：{response.text}')
        return response


# 定义加密和token生成类
class TokenGenerator:
    # Google TKK 默认值
    GOOGLE_TKK_DEFAULT = "446379.1364508470"

    @classmethod
    def init(cls):
        cls.EXT_VERSION = '400500.1364508470'
        cls.EXT_VERSION = "400601.1364508470"

    @classmethod
    def google_token(cls, e, t):
        try:
            tkk = cls.update_token(e)
            return cls._cal(t, tkk) if tkk and tkk != "" else cls._cal(t, cls.GOOGLE_TKK_DEFAULT)
        except Exception as ex:
            raise ex

    @classmethod
    def seller_sprite_token(cls, e, t, r, n):
        s = []
        a = [e, t, r, n]
        for item in a:
            if item and item is not None and len(str(item)) > 0:
                if isinstance(item, list):
                    for i in range(len(item)):
                        s.append(item[i])
                else:
                    s.append(str(item))
        return "" if len(s) < 1 else cls._cal("".join(map(str, s)), cls.EXT_VERSION)

    @classmethod
    def update_token(cls, e):
        return cls.GOOGLE_TKK_DEFAULT

    @classmethod
    def _cal(cls, e, t):
        def r(e, t):
            for i in range(0, len(t) - 2, 3):
                n = t[i + 2]
                n = ord(n) - 87 if n >= "a" else int(n)
                n = e >> n if t[i + 1] == "+" else e << n
                e = (e + n) & 4294967295 if t[i] == "+" else e ^ n
            return e

        n = t.split(".")
        t = int(n[0]) or 0
        s = []
        a = 0
        for i in range(len(e)):
            o = ord(e[i])
            if o < 128:
                s.append(o)
            else:
                if o < 2048:
                    s.append((o >> 6) | 192)
                elif 55296 == (64512 & o) and i + 1 < len(e) and 56320 == (64512 & ord(e[i + 1])):
                    o = 65536 + ((1023 & o) << 10) + (1023 & ord(e[i + 1]))
                    s.append((o >> 18) | 240)
                    s.append((o >> 12) & 63 | 128)
                else:
                    s.append((o >> 12) | 224)
                    s.append((o >> 6) & 63 | 128)
                s.append(63 & o | 128)

        e = t
        for i in range(len(s)):
            e = r(e + s[i], "+-a^+6")

        e = r(e, "+-3^+b+-f")
        e ^= int(n[1]) or 0
        if e < 0:
            e = ********** + (********** & e)

        result = e % 1000000
        return f"{result}.{result ^ t}"


# 生成asin任务的令牌
def get_task_tk(asin_str):
    TokenGenerator.init()
    tk = TokenGenerator.seller_sprite_token(None, asin_str, None, None)
    # print(f"生成tk=>{tk}")
    return tk


# 生成登录所需的tk令牌
def get_login_tk(account, passwd):
    TokenGenerator.init()
    tk = TokenGenerator.seller_sprite_token(account, calculate_md5_hash(passwd), None, None)
    # print(f"生成tk=>{tk}")
    return tk


# UUID生成函数
def generate_uuid():
    return "{:08x}-{:04x}-4{:03x}-{:03x}-{:012x}".format(
            random.getrandbits(32),
            random.getrandbits(16),
            random.getrandbits(12) | 0x4000,
            random.getrandbits(12) | 0x8000,
            random.getrandbits(48)
    )


sse = SellerSpriteExpansion()
# 示例代码
if __name__ == '__main__':
    asin_list = ['B0DM6W16H6', 'B0DP1S5HJV', 'B0DNDTS42Z', 'B0D672SVRZ']
    # asin_list = ['B0CKTSYFJH']
    # asin_list = ['B0DNZV2RXF']
    # rs = rpa_get_info('seller_sprite_user_cj', 'cookies')
    # user = rs['username']
    # pwd = rs['password']
    # print(user, pwd)
    # sse = SellerSpriteExpansion(user=user, pwd=pwd)
    # token = rpa_get_info('sse_cookies_rpa168', 'token')
    # token = "9940518271TpZWzJx/Y5wq9+YkF3WQYgWibOVAg+Ih9s9g3nAH+yi60IPCxLwlyGp431BjlOE7"
    # token = "0449253371OceK622ybSqoYxnNZav23fLZ5lDroQ5pp0CLjPpSfjw/DR8hcEIaahqdcJku5axt"
    # sse = SellerSpriteExpansion(token=token)
    for _ in range(10):
        asins_data = sse.get_list(country="us", asin_list=asin_list)
        print(asins_data)

    datas = SellerSpriteExpansion().get_list(country="us", asin_list=asin_list)
    print(datas)
