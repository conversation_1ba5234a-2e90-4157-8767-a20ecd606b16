# -*- coding:UTF-8 -*-
# @FileName  :领星产品表现采集.py
# @Time      :2024/10/11 15:17
# <AUTHOR>
from utils_mrc.pub_fun import *
import time
from threading import Thread
from work.自动化.领星抓取.LingXing import *
import work.自动化.领星抓取.LingXing


def main(lx):
    # 获取主任务
    main_tasks = MS.get_dict(
            f'SELECT * FROM `{tb_task_name}`  WHERE STATUS = 1 ORDER BY priority desc, id asc'
    )
    if not main_tasks:
        time.sleep(10)
        return
    for task in main_tasks:
        task_id = task.get('id')
        # 修改所关联任务状态为执行中
        if MS.update(f'UPDATE `{tb_task_name}` SET status = 2,`execute_time` = %s WHERE id = %s and `status` = 1', (now_int(), task_id)) < 1:
            logging(f'当前任务已经在执行')
            return False
        app_id = task.get('app_id')
        user_id = task.get('user_id')
        username = task.get('username')
        date_time = task.get('datetime') or get_today_zero_timestamp()
        data_status = 1
        params = {  # 更新任务表/数据表记录的字段
            'task_id': task_id,
            'app_id': app_id,
            'user_id': user_id,
            'username': username,
            'date_time': date_time,
            'data_status': data_status,
            'tb_task_name': tb_task_name
        }
        # 开始抓取任务
        logging(f'当前应用ID:{app_id},任务ID:{task_id}')
        lx.task_params = params
        lx.get_report_product_performance()  # 抓取数据
        # lx.run_task_get_report_product_performance()  # 抓取数据

        logging(f'任务:{task_id} 已执行结束')


def check_cron_task(param_tb_task_name):
    logging('已执行定时任务检查')

    def create_task():
        MS.insert(
                f'insert into rpa.{param_tb_task_name} (app_id,cid,platform,title,user_id,username,start_time,end_time,datetime,create_time) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
                (10, 1, 'lingxing', '每日自动抓取', 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int(),)
        )
        if not MS.err:
            logging('每日抓取任务生成任务成功')

    while True:
        current_hour = datetime.now().hour
        if not (0 <= current_hour < 7):
            create_task()
        else:
            logging('当前时间在凌晨0-6点之间，跳过任务执行')
        time.sleep(60 * 60 * 2)  # 每两小时检查一次


MS = MSXS
# work.自动化.领星抓取.LingXing.MS = MS
tb_task_name = 'task_lingxing'

if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    Thread(target=check_cron_task, args=(tb_task_name,)).start()
    lx = LingXing(local_port=9335)
    while True:
        try:
            main(lx)
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send('Asin广告位', msg, err)
