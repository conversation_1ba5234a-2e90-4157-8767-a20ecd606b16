// ==UserScript==
// @name         调整分析表盘下载
// @namespace    https://bbs.tampermonkey.net.cn/
// @version      0.1
// @description  数跨境自动化需求
// <AUTHOR>
// @match        https://work.shukuajing.com/decision/home/<USER>/dashboard/*
// @grant        GM_xmlhttpRequest
// @require      https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js
// ==/UserScript==

(async function () {
  "use strict";

  const headers = {
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    Pragma: "no-cache",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent":
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
  };

  const btn_dict = { id: "my_ctl_btn_ts", text: "推送" };
  //初始化日志框
  const logBox_id = "log-box-mrc-ts";
  const logBox = createLogBox();
  const tag_button = ".hd-components-secondary-button"; //参考插入位置按钮
  var is_downloading = false;
  const host = "erpapi.yxyglobal.com";
  // const host ='fba.zs.com'

  // 创建日志框
  function createLogBox(options = {}) {
    const {
      logBoxStyle = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 999999;
                background-color: #333;
                color: white;
                font-family: monospace;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
                max-height: 500px; /* 调整为您所需的值 */
                max-width: 1000px;
                overflow-y: auto;
                opacity: 0.8; /* 设置透明度 */
                font-family: 'Microsoft YaHei', sans-serif;
            `,
      titleBarStyle = `
                cursor: pointer;
                margin-bottom: 2px;
                user-select: none;
                position: sticky; /* 添加sticky定位 */
                top: 5px; /* 确保标题栏始终位于顶部 */
                text-align: center; /* 文字居中 */
                color: yellow;
            `,
      logContentStyle = `
                padding-top: 10px; /* 避免内容与标题栏重叠 */
            `,
      initialContent = "运行消息提示(点击关闭)",
      maxLogItems = 50
    } = options;

    let logBox = null;
    let titleBar = null;
    let logContent = null;

    function addLogMessage(text, isError = "") {
      const logItem = document.createElement("div");
      logItem.textContent = new Date().toLocaleString() + " " + text;
      const isGreen = isError.includes("success");
      logItem.style.cssText = `    color: ${
        isGreen ? "#2dbe00" : isError ? "red" : "white"
      };`;
      const existingProgressBar = document.querySelector("#mrc-progress-bar");
      if (existingProgressBar) {
        logContent.insertBefore(logItem, existingProgressBar);
      } else {
        logContent.insertBefore(logItem, null);
      }

      // 控制日志数量
      if (logContent.children.length > maxLogItems) {
        logContent.removeChild(logContent.children[0]);
      }

      // 滚动到底部
      logBox.scrollTop = logBox.scrollHeight;
    }

    function addProgressBar(message) {
      let content = new Date().toLocaleString() + " " + message;
      // 获取现有的进度条元素
      const existingProgressBar = document.querySelector("#mrc-progress-bar");

      if (existingProgressBar) {
        // 如果进度条已经存在，则更新其文本内容
        existingProgressBar.textContent = content;
      } else {
        // 如果进度条不存在，则创建一个新的进度条
        const progressBar = document.createElement("div");
        progressBar.id = "mrc-progress-bar";
        progressBar.style.cssText = `
                    color: #4dcdb8;
                `;
        progressBar.textContent = content;
        logContent.insertBefore(progressBar, null);
      }
    }

    function closeProgressBar() {
      const existingProgressBar = document.querySelector("#mrc-progress-bar");

      if (existingProgressBar) {
        // 如果进度条已经存在，则更新其文本内容
        existingProgressBar.id = "";
      }
    }

    function show() {
      logBox.style.display = "block"; // 或者 'inline-block'，根据需要选择
    }

    function clear() {
      while (logContent.firstChild) {
        logContent.removeChild(logContent.firstChild);
      }
    }

    function init() {
      if (document.getElementById(logBox_id)) {
        return;
      }
      logBox = document.createElement("div");
      logBox.id = logBox_id;
      logBox.style.cssText = logBoxStyle;
      logBox.style.display = "none"; // 默认不显示日志框

      titleBar = document.createElement("h3");
      titleBar.style.cssText = titleBarStyle;
      titleBar.textContent = initialContent;
      titleBar.addEventListener("click", () => {
        logBox.style.display = "none";
      });

      logContent = document.createElement("div");
      logContent.style.cssText = logContentStyle;

      logBox.appendChild(titleBar);
      logBox.appendChild(logContent);

      document.body.appendChild(logBox);
    }

    init();

    return {
      addLogMessage,
      show,
      clear,
      addProgressBar,
      closeProgressBar
    };
  }
  function get_token(params) {
    const CRYPT_KEY = "Oepd1OBMamXolAQXSoAetFAhwaHxXN982D";

    // 对象按key排序
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {});

    // // 自定义encodeURIComponent来匹配PHP的http_build_query编码
    // const phpUrlEncode = str => {
    //   return encodeURIComponent(str)
    //     .replace(/!/g, "%21")
    //     .replace(/'/g, "%27")
    //     .replace(/\(/g, "%28")
    //     .replace(/\)/g, "%29")
    //     .replace(/\*/g, "%2A")
    //     .replace(/%20/g, "+"); // 空格转换为+
    // };
    // // 构建参数字符串,使用自定义编码 RFC1738编码标准 用以下方式
    // const pairs = [];
    // for (const key in sortedParams) {
    //   if (sortedParams.hasOwnProperty(key)) {
    //     pairs.push(
    //       phpUrlEncode(key) + "=" + phpUrlEncode(sortedParams[key].toString())
    //     );
    //   }
    // }
    // const paramString = pairs.join("&");
    const paramString = new URLSearchParams(sortedParams).toString(); // 需要后端RFC3986编码标准才能使用

    // Base64编码并替换字符
    const base64 = btoa(paramString + CRYPT_KEY);
    const strtr = base64
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=/g, "");

    // 生成MD5
    return CryptoJS.MD5(strtr).toString();
  }

  function generateSessionID() {
    const chars = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "a",
      "b",
      "c",
      "d",
      "e",
      "f"
    ];
    let result = "";
    for (let i = 0; i < 16; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  }

  // 发送任务ID到后端并启动任务的函数
  // 发起 POST 请求
  function startTask(json_data) {
    const params = {
      c: "rpa/task",
      a: "index"
    };
    // 获取 token
    headers["X-Token"] = get_token(json_data);
    // 更新参数
    Object.assign(params, json_data);
    // 将参数转换为查询字符串
    const query = new URLSearchParams(params).toString();
    const urlWithQuery = `http://${host}/api/v1/index/get?${query}`;

    GM_xmlhttpRequest({
      method: "GET",
      url: urlWithQuery,
      headers: headers,
      credentials: "include", // 支持携带凭证
      onload: function (response) {
        try {
          const data = JSON.parse(response.responseText);
          // console.log(data);
          const tid = data.data.id;
          logBox.addLogMessage(
            `${data.msg}，ID: ${tid}。每1秒更新一次任务执行进度`,
            "success"
          );
          // 启动任务后立即设置定时器检查状态
          checkTaskStatus(tid);
          setTimeout(() => checkTaskStatus(tid), 1000);
        } catch (e) {
          logBox.addLogMessage(response.responseText);
        }
      },
      onerror: function (error) {
        console.error("Error:", error);
      }
    });
  }

  // 检查任务状态的函数
  function checkTaskStatus(tid) {
    let json_data = {
      tid: tid
    };
    const params = {
      c: "rpa/task",
      a: "getLog"
    };
    // 获取 token
    headers["X-Token"] = get_token(json_data);
    // 更新参数
    Object.assign(params, json_data);
    // 将参数转换为查询字符串
    const query = new URLSearchParams(params).toString();
    const urlWithQuery = `http://${host}/api/v1/index/get?${query}`;
    GM_xmlhttpRequest({
      method: "GET",
      url: urlWithQuery,
      headers: headers,
      credentials: "include", // 支持携带凭证
      onload: function (response) {
        const data = JSON.parse(response.responseText);
        // console.log(data);
        const msg = data.msg;

        if (data.code !== 0) {
          logBox.addLogMessage(msg, "error");
          // alert(msg);
          throw new Error(msg);
        } else if (data.code === 20 || msg.includes("执行完毕")) {
          logBox.addLogMessage(
            msg,
            ["异常", "失败"].some(s => msg.includes(s)) ? "error" : "success"
          );
          alert(`任务完成: ${msg}`);
          return;
        }

        if (msg.includes("%")) {
          logBox.addProgressBar(msg);
        } else {
          logBox.addLogMessage(msg);
        }
        setTimeout(() => checkTaskStatus(tid), 1000); // 每秒查询一次
      },
      onerror: function (error) {
        logBox.addLogMessage(
          `执行任务异常，获取执行日志失败: ${error}`,
          "error"
        );
      }
    });
  }

  async function handle_data() {
    if (is_downloading) {
      // 如果进度条已经存在，则更新其文本内容
      alert("任务正在运行，请勿重复操作！");
      return;
    }
    let confirmation = confirm(
      "警告：此操作将会清空所有数据表，任何用户修改的内容都将被重置。请确保已备份数据，否则数据将永久丢失。是否继续？"
    );
    if (!confirmation) {
      return;
    }
    logBox.show();
    is_downloading = true;

    let dashboard_id = document.location.pathname.split("/").pop();
    let bi_version;
    let username;
    try {
      bi_version = BI?.currentVersion || "";
      username = BI?.personal?.realName || "";

      if (!bi_version) {
        throw new Error("无法从页面获取当前版本信息，请联系技术人员。");
      }
    } catch (err) {
      is_downloading = false;
      logBox.addLogMessage(err.message, "error");
      alert(err.message);
      return;
    }

    let json_data = {
      dashboardId: dashboard_id,
      cookies: document.cookie,
      tid: new Date().getTime(),
      "jsy-server-version": bi_version,
      username: username
    };
    startTask(json_data);

    logBox.addLogMessage("任务已创建。");
    logBox.closeProgressBar();
    is_downloading = false;
    /*if (is_downloading) {
            // 如果进度条已经存在，则更新其文本内容
            alert('任务正在运行，请勿重复操作！')
            return;
        }
        let confirmation = confirm('警告：此操作将会清空所有数据表，任何用户修改的内容都将被重置。请确保已备份数据，否则数据将永久丢失。是否继续？');
        if (!confirmation) {
            return;
        }
        is_downloading = true;
        logBox.show();

        let dashboard_id = document.location.pathname.split('/').pop();
        let data_json = {
            "dashboardId": dashboard_id, "cookies": document.cookie, "tid": new Date().getTime()
        }
        logBox.addLogMessage("任务已接收，正在执行任务...")
        logBox.addLogMessage("数据实时更新，请前往 https://va81rsh3m92.feishu.cn/base/EKR7bRB24aZIKFszqO5cn3mCn9d 查看进度。")
        try {
            startTask(data_json);
        } catch (error) {
            logBox.addLogMessage(error, 'error')
        }
        logBox.closeProgressBar();
        is_downloading = false;*/
    /*
        let dashboard_id = document.location.pathname.split('/').pop();
        const params_common = {
            url: `http://fetch01:9999/skj`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                "dashboardId": dashboard_id, // 假设 dashboard_id 已经定义
                "cookies": document.cookie,
                "fine_auth_token": fine_auth_token, // 假设 fine_auth_token 已经定义
                "authorization": fine_auth_token
            }),
            onload: function (response) {
                console.log("Response received:", response.responseText);
                logBox.addLogMessage(response.responseText, 'success')
                logBox.closeProgressBar()
                alert(response.responseText)
            },
            onerror: function (error) {
                console.error("Error occurred:", error);
                logBox.addLogMessage(error, 'error')
            }
        };


        // params_common.body = JSON.stringify(data_json)
        logBox.addLogMessage("任务已接收，正在执行任务...")
        logBox.addLogMessage("数据实时更新，请前往 https://va81rsh3m92.feishu.cn/base/EKR7bRB24aZIKFszqO5cn3mCn9d 查看进度。")
        try {
            // let result1 = await get_data(params_common);
            // 发送请求
            GM_xmlhttpRequest(params_common);
        } catch (error) {
            logBox.addLogMessage(error, 'error')
        }

        socket.disconnect();// 断开Socket.IO连接
        is_downloading = false;
        */

    /*
        // console.log('Async Result:', result1)
                params_common.url = `https://work.shukuajing.com/decision/v1/group/switch/shorturl/${dashboard_id}?type=1&entryType=1`
                params_common.method = 'GET'
                let result = await get_data(params_common);
                console.log('Async Result:', result);
                let rs_successful = result.success
                if (!rs_successful || !result.data?.groupId) {
                    logBox.addLogMessage(`获取分组ID失败${result}`, 'error')
                    return ''
                }
                let {groupId, groupName} = result.data


                // params_common.url = `https://work.shukuajing.com/decision/v1/design/report/8c98f15ca2f04109873de2cfd82da4d0/config?groupId=2d7d87933d5a42f5a7cb680a396a5d34&ignoreVisit=false&resourceSpace=0`
                params_common.url = `https://work.shukuajing.com/decision/v1/design/report/${dashboard_id}/config?groupId=${groupId}&ignoreVisit=false&resourceSpace=0`
                params_common.method = 'GET'
                result = await get_data(params_common);
                console.log('Async Result:', result);
                rs_successful = result.success
                if (!rs_successful) {
                    logBox.addLogMessage(`获取配置失败${result}`, 'error')
                    return ''
                }

                let {widgets, extends: extendsProp} = result.data?.designConfigure || {};

                // 新建一个数组用于存储结果
                const resultTabs = [];
                // 遍历extendsProp对象，找到所有的tabs
                for (const key in extendsProp) {
                    const tabData = extendsProp[key].tabs;
                    if (tabData) {
                        // 遍历tabs数组
                        tabData.forEach(tab => {
                            // 将所需的数据添加到结果数组中
                            resultTabs.push({
                                id: tab.id,
                                name: tab.name,
                                item_list: Object.keys(tab.widgets).map(wId => ({
                                    [wId]: widgets[wId]?.widget.name.childNodes[0].nodeValue || '未知名称'
                                })),
                                item_obj: Object.keys(tab.widgets).reduce((acc, wId) => {
                                    acc[wId] = widgets[wId]?.widget.name.childNodes[0].nodeValue || '未知名称';
                                    return acc;
                                }, {})
                            });
                        });
                    }
                }

                // let list = []
                logBox.addLogMessage(`获取配置成功，共${Object.keys(widgets).length}个组件`)
                for (let key in widgets) {
                    let {widget} = widgets[key]
                    widget['visible'] = true
                    widget['filterValue'] = {
                        "filterType": 34,
                        "filterValue": []
                    }
                    widget['widgetIdValueMap'] = {}
                    widget['dimensionAuthorities'] = []
                    widget["page"] = 1
                    // list.push(widget)
                    let operators = widgets[key]?.operators
                    let tb_name = widget.name.childNodes[0]?.nodeValue
                    let wId = widget.wId
                    params_common.url = `https://work.shukuajing.com/decision/v1/design/widget/${dashboard_id}/data`
                    params_common.method = 'POST'

                    let totalPages = 0; // 总页数

                    while (true) {
                        // 构建请求体
                        let body = {
                            'sessionId': sessionid,
                            'widget': widget,
                            'operators': operators,
                        };
                        params_common.body = JSON.stringify(body); // 将请求体序列化为JSON字符串

                        let tb_rs = await get_data(params_common)
                        if (!tb_rs.success) {
                            logBox.addLogMessage(`获取表格数据失败${tb_rs}`, 'error')
                            return ''
                        }
                        let totalRows = tb_rs.data.row; // 总记录数
                        if (totalRows > 0) {
                            totalPages = Math.ceil(totalRows / 100); // 计算总页数
                            let tb_data = extractTableData(tb_rs.data)
                            let filename = `${tb_name}_${widget.page}页`
                            exportExcel(tb_data, filename)
                            logBox.addLogMessage(`${tb_name} 第${widget.page}页 成功获取数据;共${totalRows}条数据;总页数${totalPages};剩余${totalPages - widget.page}页`);

                            // if (currentPage < totalPages) {
                            //     widget.page++; // 修改widget中的页码
                            // } else {
                            //     break; // 如果当前页的记录数小于100，则认为已经是最后一页
                            // }
                            break
                        } else {
                            logBox.addLogMessage(`${tb_name}没有数据`)
                            break
                        }
                    }
                }
                // console.log(list)
                alert('ok')*/
  }

  async function add_button_addAds() {
    // 创建一个新的按钮元素
    var myButton = document.createElement("button");
    myButton.textContent = btn_dict.text;
    myButton.id = btn_dict.id;
    // 设置按钮样式
    myButton.className =
      "bi-basic-button cursor-pointer hd-components-secondary-button bi-button bi-f-c bi-f-h v-middle h-center light button-common f-s-n c-e";
    // myButton.style.height = '32px';
    // myButton.style.backgroundColor = 'green';
    // myButton.style.marginLeft = '10px';
    // myButton.style.zIndex = '99999'; // 可视层级高于其他元素
    myButton.style.cssText =
      "height: 30px;min-width: 70px;position: relative;margin-left: 15px;background-color: #5cff5ca5;";

    // 添加点击事件监听器
    myButton.addEventListener("click", async function () {
      await handle_data();
    });

    // 找到合适的位置插入按钮
    const operateSection = document.querySelector(tag_button);
    if (operateSection) {
      // operateSection.parentNode.insertBefore(myButton, operateSection.nextSibling);
      operateSection.parentNode.insertBefore(myButton, operateSection); // 插入到按钮前面
      // operateSection.parentNode.appendChild(myButton); // 插入到按钮后面
    } else {
      logBox.addLogMessage(`未找到具有 ${tag_button} 元素`, "error");
    }
  }

  // 页面加载完成后，将按钮添加到页面
  window.addEventListener("load", () => {
    const timer = setInterval(function () {
      const operateSection = document.querySelector(tag_button);
      if (operateSection) {
        clearInterval(timer); // 停止定时器
        add_button_addAds();
      }
    }, 1000); // 每1000毫秒（1秒）检查一次
  });
})();
