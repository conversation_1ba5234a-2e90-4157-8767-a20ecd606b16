import random

from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

df = pd.read_excel(r'D:\Downloads\需要查询状态Asin-0311.xlsx')
# 将列名转换为小写
df.columns = df.columns.str.lower()
# 对 'asin' 列进行去重
unique_asins = df['asin'].drop_duplicates()
unique_asins = unique_asins.tolist()
# unique_asins = random.sample(unique_asins, 20)  # 生成随机10
asins = '、'.join(unique_asins)
print(unique_asins[:5], f'共{len(unique_asins)}个ASIN')

app_id = 5  # 状态
# app_id = 8  # 五点等详情

MSXS.insert("""
INSERT INTO `rpa`.`task_listing` (`app_id`, `site`, `title`, `asins`, `user_id`, `username`, `start_time`, `end_time`, `datetime`, `create_time`) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);
""", (app_id, 'de', f'asin {now_str()}', asins, 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int(),))
