# -*- coding:UTF-8 -*-
# @FileName  :main.py
# @Time      :2024/6/18 11:27
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.数跨境.SKJ import *
from utils_mrc.MysqlHelper import *
from threading import Lock

MS = MSXS
task_table = 'task_shukuajing'
task_log_table = 'task_shukuajing_log'
# 锁，用于线程安全地操作消息队列
lock = Lock()


class MyStdout:
    def __init__(self, tid, original_stdout):
        self.tid = tid
        self.original_stdout = original_stdout
        self.last_time = now_int()
        self.last_content = ''
        self.min_interval = 2

    def write(self, message):
        self.original_stdout.write(message)
        with lock:
            msg = message.strip()
            if msg:
                # if '任务执行完毕' not in message and now_int() - self.last_time < self.min_interval and '%' in message and '100%' not in message:
                #     return
                if msg == self.last_content:
                    return
                MS.insert(f'insert into rpa.`{task_log_table}` (tid,contents,create_time) values (%s,%s,%s)', (self.tid, msg, now_int()))
                self.last_time = now_int()
                self.last_content = msg

    def recover(self):
        sys.stdout = sys.__stdout__

    def flush(self):
        pass


def main():
    # 获取主任务
    task = MS.get_dict_one(
            f'SELECT id, app_id, params, user_id, username, datetime, site  FROM `{task_table}` WHERE STATUS = 1 limit 1'
    )
    if not task:
        time.sleep(1)
        return
    tid = task.get('id')
    # 修改所关联任务状态为执行中
    if MS.update(f'UPDATE `{task_table}` SET status = 2,`execute_time` = %s WHERE id = %s and `status` = 1', (now_int(), tid)) < 1:
        logging(f'当前任务已经在执行')
        # return False
    app_id = task.get('app_id')
    task_params = task.get('params')
    task_params = json.loads(task_params)

    # 开始抓取任务
    logging(f'当前应用ID:{app_id},任务ID:{tid}')

    my_stdout = MyStdout(tid, sys.stdout)  # 创建自定义输出对象
    sys.stdout = my_stdout

    skj = SKJ(task_params)
    result_data = skj.main()

    task_num = result_data['total']  # 任务数量
    status = result_data['status']
    result = result_data['result']
    print(result)

    my_stdout.recover()  # 恢复默认输出

    MS.update(f'UPDATE `{task_table}` SET status = %s,`done_time` = %s, task_num =%s,result=%s  WHERE id = %s', (status, now_int(), task_num, result, tid))
    if MS.err:
        logging(f'更新任务状态失败:{MS.err}')
        return
    logging(f'任务:{tid} 已执行结束')


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    while True:
        try:
            main()
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send('数跨境推送飞书', msg, err)
