# -*- coding:UTF-8 -*-
import schedule
from utils_mrc.pub_fun import *
from threading import Thread
from work.自动化.紫鸟.ZiNiao import *

MS = MSXS


def main():
    # if MS.get_one(f'SELECT 1 FROM rpa.`data_amazon_top_search_terms` where datetime  >= {get_last_saturday(f="i")} limit 1'):
    #     logging(f"目标数据已存在！本次任务忽略！")
    #     return

    manager = StoreManager()

    browser_list = manager.get_browser_list()
    if browser_list is None:
        fsmsg.send(None, "商店列表获取失败！")
        exit()

    store_name = 'AM_ZF(EU)'
    store = next((x for x in browser_list if x['browserName'] == store_name)) or exit('未找到指定店铺')

    manager.task_download_top_search_terms(store)


if __name__ == "__main__":
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')

    main()  # 先执行一次

    # 每周的某个时间点执行任务
    schedule.every().monday.at("07:30").do(main)

    while True:
        schedule.run_pending()
        time.sleep(1)
