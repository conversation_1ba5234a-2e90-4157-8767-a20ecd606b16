# -*- coding:UTF-8 -*-
from utils_mrc.pub_fun import *
from work.自动化.领星抓取.LingXingSessionPage import *
from multiprocessing import Process

users = ['yxy023', 'yxy025']  # 抓取账户
tb_name = "rpa.`data_lingxing_report_generate`"
lx = LingXingSessionPage()


def main():
    # 获取主任务
    main_tasks = MS100.get_dict(f'select unique_id,request_param from {tb_name} where fetch_status=1')
    if not main_tasks:
        time.sleep(10)
        return

    lx.group_report_tasks(users, 6, 'fba库存明细')  # 任务分组-fba库存明细
    processes = []
    for user in users:
        process = Process(target=main_run_task_report_generate, args=(user,))
        process.start()
        processes.append(process)

    # 等待所有进程结束
    for process in processes:
        process.join()


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    while True:
        try:
            main()
        except:
            err = traceback.format_exc()
            print(err)
            fsmsg.send('订单/库存报表生成主程序抓取异常！', '订单/库存报表生成主程序抓取异常！', err)
