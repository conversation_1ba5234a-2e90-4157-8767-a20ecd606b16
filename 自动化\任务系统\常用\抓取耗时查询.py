from utils_mrc.MysqlHelper import *
import datetime


def get_task_every_day(start_date, end_date):
    # 将日期转换为Unix时间戳
    start_timestamp = int(datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S').timestamp())
    end_timestamp = int(datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S').timestamp())

    rs = MSXS.get_dict("SELECT * FROM `rpa`.`task_goods` WHERE execute_time >= %s AND execute_time <= %s AND done_time > 0 AND execute_time > 0", (start_timestamp, end_timestamp))
    for r in rs:
        id = r['id']
        platform = r['platform']
        page = r['page']
        platform_num = r['platform_num']
        task_num = r['task_num']
        execute_time = r['execute_time']
        done_time = r['done_time']
        # 计算时间差
        time_diff = done_time - execute_time

        # 将时间差转换为datetime.timedelta对象
        delta = datetime.timedelta(seconds=time_diff)
        # print(delta)

        # 打印结果
        logging(f"{id}\t{page}页\t来源:{platform}\t平台数量:{platform_num}\t任务数量:{task_num}\t时间差为：{delta}秒")


def get_daily_average_duration(start_date, end_date=None):
    # 将日期转换为Unix时间戳
    start_timestamp = int(datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S').timestamp())
    if end_date is None:
        end_timestamp = int(datetime.datetime.now().timestamp())
    else:
        end_timestamp = int(datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S').timestamp())

    # 查询指定时间段内每天的平均耗时
    query = """
    SELECT 
        # create_time AS date,
        FLOOR(create_time / 10000) * 10000 AS date,
        # DATE(FROM_UNIXTIME(create_time)) AS date,
        DATE(FROM_UNIXTIME(datetime)) AS curdate,
        COUNT(*) AS task_count,
        max(done_time) - min(execute_time) AS total_duration,
        # AVG(done_time - execute_time) AS average_duration
        (max(done_time) - min(execute_time))/COUNT(1) AS average_duration
    FROM 
        `rpa`.`task_asins_loc`
    WHERE 
        execute_time >= %s AND done_time <= %s
        AND done_time > 0 AND execute_time > 0
        # and execute_time>UNIX_TIMESTAMP('2024-12-13 12:00:00')
    GROUP BY 
        date
    ORDER BY 
        date
    """

    rs = MSXS.get_dict(query, (start_timestamp, end_timestamp))

    for r in rs:
        curdate = r['curdate']
        date = r['date']
        task_count = r['task_count']
        average_duration = r['average_duration']
        total_duration = r['total_duration']

        # 将 average_duration 转换为浮点数
        average_duration_float = float(average_duration)
        total_duration_float = float(total_duration)

        # 将平均耗时转换为datetime.timedelta对象
        avg_delta = datetime.timedelta(seconds=average_duration_float)
        total_delta = datetime.timedelta(seconds=total_duration_float)

        print(f"日期: {curdate}, 时间戳: {date}, 任务数量: {task_count}, 总耗时: {total_delta}, 平均耗时: {avg_delta}")


# 示例调用
start_date = '2024-11-15 00:00:00'
get_daily_average_duration(start_date)
# get_task_every_day(start_date, end_date)
