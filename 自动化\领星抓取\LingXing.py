# -*- coding:UTF-8 -*-
# @FileName  :LingXing.py
# @Time      :2024/7/22 9:46
# <AUTHOR>
from utils_mrc.pub_fun import *
import copy
import random
import re
import sys
import time
import traceback
import json
import urllib.parse
import pandas as pd
from datetime import timedel<PERSON>
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage, Chromium
from DrissionPage.errors import *
from tqdm import tqdm
from utils_mrc.MysqlHelper import *
from utils_mrc.FeiShuAPI import *
from utils_mrc.AccountManager import *
from TimePinner import Pinner
from DownloadKit import DownloadKit
import dateutil.parser


class LingXing:

    def __init__(self, local_port=None, account=None, username=None, password=None):
        self.headers_erp = None
        self.local_port = local_port
        self.username = username
        self.password = password
        account = account or rpa_get_info('lingxing_user2')
        if account:
            info_data = json.loads(account.get('info_data'))
            self.username = info_data.get('username')
            self.password = info_data.get('password')
            self.local_port = local_port or account['port'] or 9222
        logging(f'账号信息: {self.username},使用端口: {self.local_port}')
        self.company_id = "901122764666708480"
        self.cookies_erp = rpa_get_info(f'lingxing_erp_cookies%{self.local_port}', 'info_data')
        self.cookies_ad = rpa_get_info(f'lingxing_ad_cookies%{self.local_port}', 'info_data')
        self.login_ad_url = 'https://muke.lingxing.com/erp/adverAuthLogin?adver_url=/erp/login'  # 登录广告页
        self.home_erp = 'https://muke.lingxing.com/home'
        self.home_ad = 'https://ads.lingxing.com/home'
        self.tb_fields = {}
        self.total_records = 0
        co = ChromiumOptions()
        co.remove_extensions()
        co.set_local_port(self.local_port)
        self.co = co
        self.page = ChromiumPage(addr_or_opts=co)
        self.page.get(self.home_erp)
        # self.page.set.window.show()
        # self.page.set.window.max()
        self.page.set.window.mini()
        self.page.set.timeouts(20)
        self.sp_ad = SessionPage()
        self.sess_ad = self.sp_ad.session
        self.sp_erp = SessionPage()
        self.sess_erp = self.sp_erp.session
        self.set_session_cookies()  # 设置session的cookies
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36",
        }
        self.headers_ad = {
            **self.headers,
            "Connection": "keep-alive",
            "Content-Type": "application/json;charset=UTF-8",
            "Origin": "https://ads.lingxing.com",
            # "Referer": "https://ads.lingxing.com/home",
        }
        self.headers_erp = {
            **self.headers,
            "ak-client-type": "web",
            "ak-origin": "https://muke.lingxing.com",
            "auth-token": get_token(self.cookies_erp),
            "origin": "https://muke.lingxing.com",
            # "referer": "https://muke.lingxing.com/",
            "x-ak-company-id": self.cookies_erp.get('company_id') or self.company_id,
        }
        # self.check_cookies_status()
        self.check_erp_user()
        self.cur_info = {
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'page_count': 0,
            'end_page_num': 0,
            'total': 0,
            'cur_page': 1,
            'report_date': get_date_range_str(),
        }
        self.task_params = {}
        self.is_into_db_product_performance = True  # 产品表现表格数据 是否写入数据库

    def login(self):
        self.page.get('https://muke.lingxing.com/login')
        if '/login' not in self.page.url:
            print(f'{self.username}登录erp成功！')
        elif self.page('tag:input@@name=account'):
            self.page('tag:input@@name=account').input(self.username, True)
            self.page('tag:input@@name=pwd').input(self.password, True)
            self.page('tag:span@@class=text-wrap@@text()=登录').click()
            self.page.wait.load_start()
            time.sleep(1)
        self.save_cookies_erp_and_ad()
        self.set_session_cookies()

    def logout(self):
        self.page.set.cookies.clear()
        self.sp_ad.set.cookies.clear()
        self.sp_ad.set.cookies.clear()

    def goto_ad(self):
        while True:
            tab = self.page.new_tab('https://ads.lingxing.com/ad_report/ad/profile/index')
            try:
                if '广告' in tab.title:
                    logging('广告页登录成功')
                    tab.run_cdp_loaded('Page.bringToFront')  # 将页面置于最前面（激活选项卡）
                    break
                elif '尚未登录' in tab.title:
                    tab.get('https://muke.lingxing.com/login')
                if 'lingxing' not in tab.url:
                    tab.get('https://muke.lingxing.com/login')
                    time.sleep(2)
                if 'lingxing.com/login' in tab.url:
                    self.login()
                    time.sleep(2)
                if 'erp/home' in tab.url:
                    time.sleep(2)
                    tab.get(self.login_ad_url)
                    new_tab = self.page.get_tab(self.page.wait.new_tab())
                    new_tab.wait.load_start()
                    new_tab.close()
                    return new_tab
            except PageDisconnectedError:
                traceback.print_exc()
                return
            except:
                traceback.print_exc()
                return
            return tab

    def set_session_cookies(self):
        if self.page:
            self.page.set.cookies(self.cookies_erp)
            self.page.refresh()
        self.sp_erp.set.cookies(self.cookies_erp)
        self.sp_erp.set.headers({"auth-token": get_token(self.cookies_erp)})
        self.sess_erp.cookies.update(self.cookies_erp)
        self.sess_erp.headers.update({"auth-token": get_token(self.cookies_erp)})

        self.sp_ad.set.cookies(self.cookies_ad)
        self.sp_ad.set.retry_interval(3)
        self.sess_ad.cookies.update(self.cookies_ad)

    def check_cookies_status(self):
        self.check_erp_user()
        self.check_ad_user()

    def check_erp_user(self):
        url = "https://gw.lingxingerp.com/newadmin/api/user/manage/myInfo"
        params = {
            "req_time_sequence": "/newadmin/api/user/manage/myInfo$$3"
        }
        self.sp_erp.set.headers(self.headers_erp)
        self.sess_erp.headers.update(self.headers_erp)
        self.sp_erp.get(url, params=params)
        if '已过期' in self.sp_erp.html or self.sp_erp.json.get('code') != 1:
            logging(f'当前登录账号erp已过期，尝试登录重新获取...')
            self.login()
        # else:
        #     logging(f'当前登录账号erp有效，继续执行...')

    def check_ad_user(self):
        url = "https://ads.lingxing.com/ad_report/keyword_grab/index/get_expired_quotas_info"
        self.sp_ad.set.headers(self.headers_ad)
        self.sess_ad.headers.update(self.headers_ad)

        self.sp_ad.set.cookies(self.cookies_ad)
        self.sp_ad.post(url)

        json_data = self.sp_ad.json

        if not json_data or not json_data.get('successful') or any(keyword in str(json_data) for keyword in ['已过期', '未登录', '登录超时']):
            logging(f'当前登录账号ads已过期，尝试登录重新获取...')
            self.login()
        # else:
        #     logging(f'当前登录账号ads有效，继续执行...')

    def save_cookies_erp_and_ad(self):
        self.page.get(self.home_erp)
        time.sleep(1)
        self.cookies_erp = self.page.cookies().as_dict()
        source = f'lingxing_erp_cookies_{self.username}_{self.local_port}'
        rpa_save_info(source, self.cookies_erp)

        self.page.get(self.home_ad)
        if '尚未登录' in self.page.title:
            self.page.get(self.login_ad_url)
            new_tab_ad = self.page.get_tab(self.page.wait.new_tab())
            new_tab_ad.wait.load_start()
            self.page.get(self.home_ad)
            new_tab_ad.close()
        time.sleep(2)
        self.cookies_ad = self.page.cookies().as_dict()
        source = f'lingxing_ad_cookies_{self.username}_{self.local_port}'
        rpa_save_info(source, self.cookies_ad)

    def fetch_profile_list(self):
        self.check_ad_user()
        url = "https://ads.lingxing.com/common/common_list/common_list/get_profile_list"
        data = {
            'is_with_report_date': '0',
        }
        response = self.sess_ad.post(url, data=data)
        json_data = response.json()
        if json_data.get('successful'):
            sql = """
                INSERT INTO rpa.data_lingxing_account (alias, profile_id, country, type, create_time)
                VALUES (%(alias)s, %(profile_id)s, %(country)s, %(type)s, UNIX_TIMESTAMP())
                ON DUPLICATE KEY UPDATE
                    alias=VALUES(alias),
                    country=VALUES(country),
                    type=VALUES(type)
            """
            datas = json_data.get('data') or []
            MS.insert_many(sql, datas)
            if MS.err:
                print(f"{self.cur_info['alias']} 数据操作失败 {MS.err}")
            else:
                print(f"{len(datas)}条数据操作成功")

    def save_data(self, list_data, table_name):
        # 构建 SQL 插入语句
        columns = ', '.join([f'`{col}`' for col in list_data[0].keys()])
        values = ', '.join(['%({})s'.format(k) for k in list_data[0].keys()])
        update_clause = ', '.join([f'`{k}`=VALUES(`{k}`)' for k in list_data[0].keys() if k != 'id'])

        sql = f"""
            INSERT INTO `{table_name}` ({columns})
            VALUES ({values})
            ON DUPLICATE KEY UPDATE
                {update_clause}
        """
        rs = MS.insert_many(sql, list_data)

        if MS.err:
            self.cur_info['finally_status'] = 20
            self.cur_info['result'] = f"【{self.cur_info['alias']}】店铺;【{table_name}】表;数据操作失败在第{self.cur_info['cur_page']}页;错误信息:{MS.err}"
            self.cur_info['finally_result'] += self.cur_info['result']
            logging(self.cur_info['result'])
            print(MS.err)
            fsmsg.send(content=self.cur_info['result'], except_info=MS.err)
        return rs

    def fetch_ad_report(self, profile_id="", url="", table_name='', page=1, is_fetch_all=True):
        try:
            params = {
                "ajax": ""
            }
            data = {
                "draw": str(random.randint(1, 10)),
                "start": str((page - 1) * 250),  # Calculate start based on page number
                "length": "250",
                "profile_id": profile_id,
                "page": str(page),
                "report_date": self.cur_info['report_date'],
                'serving_status[]': [
                    'TARGETING_CLAUSE_STATUS_LIVE',
                    'AD_STATUS_LIVE',
                ],
                "state": 'enabled',
                'target_state': 'enabled',
            }
            if 'ad_report/ad/profile' in url:  # SP 中 广告页签 https://ads.lingxing.com/ad_report/ad/profile/index 选择除已暂停和已归档外
                data['serving_status[]'] = [
                    'AD_STATUS_LIVE',
                    'OUT_OF_BUDGET',
                    'MISSING_IMAGE',
                    'MISSING_DECORATION',
                    'NOT_BUYABLE',
                    'NOT_IN_BUYBOX',
                    'OUT_OF_STOCK',
                    'INELIGIBLE',
                    'PORTFOLIO_PENDING_START_DATE',
                    'PORTFOLIO_PAUSED',
                    'PORTFOLIO_ENDED',
                    'PORTFOLIO_ARCHIVED',
                    'SCHEDULED',
                    'PENDING_REVIEW',
                    'ENDED',
                    'REJECTED',
                    'CAMPAIGN_INCOMPLETE',
                    'AD_GROUP_LOW_BID',
                    'ADVERTISER_PAYMENT_FAILURE',
                    'OTHER',
                ]

            try:
                self.sess_ad.headers.update({
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                })
                res = self.sess_ad.post(url, params=params, data=data, timeout=300)
            except:
                traceback.print_exc()
                res = {}
            if res and res.status_code == 200 and res.text:
                json_data = res.json()
            else:
                self.sp_ad.set.headers({
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                })
                self.sp_ad.post(url, params=params, data=data, timeout=300)
                json_data = self.sp_ad.json or {}
            status = json_data.get('successful')
            response_data = json_data.get('data') or []
            recordsFiltered = json_data.get('recordsFiltered') or 0
            if page == 1:
                logging(f'当前店铺{self.cur_info["alias"]},{table_name}报表,共有{recordsFiltered}条数据')

            if not self.cur_info['ac_tb_total'] and recordsFiltered:
                self.cur_info['ac_tb_total'] = recordsFiltered
                self.cur_info['item_count'][table_name]['total'] = recordsFiltered
                self.cur_info['item_count'][table_name]['pages'] = int(recordsFiltered / 250) + 1
                self.cur_info['item_count'][table_name]['size'] = 250
                # 初始化进度条，如果 total_records 已知
                self.cur_info['ac_tb_pbar'] = tqdm(total=recordsFiltered, desc=f'【{table_name}】报表', unit='条',
                                                   postfix=f'【{self.cur_info["alias"]}】 {self.cur_info["fetch_index"]} / {self.cur_info["fetch_total"]}', mininterval=10,
                                                   file=sys.stdout, position=1)
            # 剩余页数
            self.cur_info['page_count'] = int(recordsFiltered / 250) + 1
            self.cur_info['cur_page'] = page
            self.cur_info['params'] = data
            # logging(f'当前店铺{self.cur_info["alias"]},{table_name}报表,第{page}页,剩余{int(recordsFiltered / 250) + 1}页,共有{len(response_data)}条数据')
            if not status:
                self.cur_info['finally_status'] = 20
                self.cur_info['result'] = f'{self.cur_info["alias"]} 获取广告【{table_name}】报表失败在第{page}页'
                self.cur_info['finally_result'] += self.cur_info['result']
                logging(self.cur_info['result'])
                # pg.alert('请处理异常信息！')
                time.sleep(1)
                return False
            if not response_data:
                # logging(f'当前店铺 {table_name}没有数据')
                return True
            # 更新进度条
            if self.cur_info['ac_tb_pbar']:
                self.cur_info['ac_tb_pbar'].update(len(response_data))  # 每次调用更新进度

            list_data = self.parse_data(response_data)
            self.save_data(list_data, table_name)
            self.total_records += len(list_data)  # 累计记录数
            self.cur_info['total_count'] += len(list_data)  # 单店铺累计记录数
            self.cur_info['item_count'][table_name]['fetch_num'] += len(list_data)  # 单店铺单表累计记录数

            if len(list_data) == 250 and is_fetch_all:  # 如果最后一页的记录少于 250 条，则该页结束
                return self.fetch_ad_report(profile_id, url, table_name, page + 1)  # 用下一页递归调用 fetch_ad_report
            else:
                # 结束进度条
                if self.cur_info['ac_tb_pbar']:
                    self.cur_info['ac_tb_pbar'].close()
                logging(
                        f"{table_name} 所有数据已获取完毕，共{recordsFiltered}条记录，当前店铺累计获取{self.cur_info['total_count']}条记录，所有店铺累计获取{self.total_records}条记录")
                return True
        except Exception as e:
            self.cur_info['finally_status'] = 20
            self.cur_info['result'] = f'{self.cur_info["alias"]} 获取广告{table_name}，在第{page}页发生错误: {str(e)}'
            self.cur_info['finally_result'] += self.cur_info['result']
            logging(self.cur_info['result'])
            fsmsg.send('领星广告抓取', self.cur_info['result'], traceback.format_exc())
            traceback.print_exc()
            return False

    def parse_data(self, data):
        if not data:
            return []
        first_key_of_first_dict = next(iter(data[0]))  # 获取第一个字典的第一个键
        first_key_of_second_dict = next(iter(data[1]))  # 获取第二个字典的第一个键
        if first_key_of_first_dict != first_key_of_second_dict:  # 比较两个字典的第一个键
            data.pop(0)
        # # 检查列表的第一项，如果存在'clicks'键，则删除整个字典项
        # data.pop(0)

        # 创建一个新的列表来存放处理后的数据
        datas = []

        # 获取所有键名的集合，并转换键名
        all_keys = set().union(*[set(convert_key(k) for k in d.keys()) for d in data])

        for item in data:
            # 创建一个新字典，用于存储转换后的键值对
            nk = {}

            # 添加所有必需的键名，如果不存在则使用默认值
            for key in all_keys:
                if self.tb_fields and key not in self.tb_fields[self.cur_info['ac_tb_name']]:
                    continue
                nk[key] = item.get(key.replace('_', '').capitalize(), '')  # 默认值为空字符串

            for key, value in item.items():
                # 转换键名为小写并用下划线分隔
                new_key = convert_key(key)

                if self.tb_fields and new_key not in self.tb_fields[self.cur_info['ac_tb_name']]:
                    continue

                if value is None:
                    # 检查字段是否应该是整数类型，如果是，则转换为0，否则转换为空字符串
                    if new_key in numeric_fields:  # 假设这些是整数类型的字段
                        nk[new_key] = 0
                    else:
                        nk[new_key] = ''
                elif isinstance(value, list) or isinstance(value, dict):
                    nk[new_key] = json.dumps(value, ensure_ascii=False, separators=(",", ":"))
                else:
                    nk[new_key] = value.rstrip('%') if isinstance(value, str) else value

            # 添加 unique_id 键，如果 entity_level_hash 存在的话
            if 'entity_level_hash' in nk:
                nk['unique_id'] = nk['entity_level_hash']
            elif 'st_md5' in nk:
                nk['unique_id'] = nk['st_md5']
            nk['store_name'] = self.cur_info['alias']

            # 将处理后的字典添加到新的列表中
            datas.append(nk)

        return datas

    def fetch_ad_data(self, is_test=False):
        MS.update('UPDATE data_lingxing_account set status = 1 where status not in (1,99) and update_time < CURRENT_DATE')  # 先把非当天的数据抓取状态重置
        if is_test:
            # shop_names = tuple(['AM_ZXL-FR'])
            shop_names = tuple(['AM_AK-DE'])

            query = 'SELECT id, profile_id, alias, country FROM rpa.`data_lingxing_account` where alias in %s'
            accounts = MS.get_dict(query, (shop_names,))
            report_urls = [
                # ("https://ads.lingxing.com/ad_report/ad/profile/index", 'data_lingxing_adv_asin'),  # 广告
                # ("https://ads.lingxing.com/ad_report/target/auto/index", 'data_lingxing_adv_auto'),  # 自动投放
                # ("https://ads.lingxing.com/ad_report/keyword/profile/index", 'data_lingxing_adv_keyword'),  # 关键词
                # ("https://ads.lingxing.com/ad_report/target/profile/index", 'data_lingxing_adv_goods'),  # 商品投放
                # ("https://ads.lingxing.com/ad_report/search_term_st/profile/index", 'data_lingxing_adv_search_term'),  # 用户搜索词
                ("https://ads.lingxing.com/ad_report/search_term_asin/profile/index", 'data_lingxing_adv_search_asin'),  # 用户搜索词
            ]
        else:
            query = 'SELECT id, profile_id, alias, country FROM rpa.`data_lingxing_account` where `status`=1 ORDER BY total_count'
            accounts = MS.get_dict(query)
            report_urls = [
                ("https://ads.lingxing.com/ad_report/ad/profile/index", 'data_lingxing_adv_asin'),  # 广告
                ("https://ads.lingxing.com/ad_report/target/auto/index", 'data_lingxing_adv_auto'),  # 自动投放
                ("https://ads.lingxing.com/ad_report/keyword/profile/index", 'data_lingxing_adv_keyword'),  # 关键词
                ("https://ads.lingxing.com/ad_report/target/profile/index", 'data_lingxing_adv_goods'),  # 商品投放
                # ("https://ads.lingxing.com/ad_report/search_term_st/profile/index", 'data_lingxing_adv_search_term'),  # 用户搜索词
                ("https://ads.lingxing.com/ad_report/search_term_asin/profile/index", 'data_lingxing_adv_search_asin'),  # 用户搜索词
            ]

        for url, table_name in report_urls:  # 取所有表字段
            fields = sql_get_table_fields(table_name, MS)
            self.tb_fields[table_name] = fields
        if not self.tb_fields:
            logging('没有找到任何表字段')
            fsmsg.send(content='没有找到任何表字段,请确认数据库表名是否正确！')
            return

        if accounts:
            start_num = 0
            counter = 0  # 初始化当前抓取店铺计数器
            # 更新所有关联任务状态为执行中
            pbar = tqdm(accounts, desc="广告数据抓取", mininterval=1, unit='个')
            for index, store in enumerate(pbar, start=1):  # 添加进度条
                if index < start_num:
                    continue
                # 修改所有关联任务状态为执行中
                store_id = store['id']
                if MS.update('UPDATE `data_lingxing_account` SET status = 2 WHERE id = %s and `status` = 1', store_id) < 1 and not is_test:
                    # logging(f'{store["alias"]}已经在执行')
                    continue
                tqdm.write('\n')
                counter += 1  # 更新计数器
                pbar.set_postfix({'实抓店铺数': counter})
                profile_id = store['profile_id']
                logging(f'开始抓取 {store["alias"]}')
                pin.pin(f'{store["alias"]} 开始记录')
                self.cur_info['profile_id'] = profile_id
                self.cur_info['alias'] = store['alias']
                self.cur_info['fetch_index'] = index
                self.cur_info['fetch_total'] = len(accounts)
                self.cur_info['total_count'] = 0
                self.cur_info['item_count'] = {}
                self.cur_info['finally_status'] = 10
                for url, table_name in report_urls:
                    self.cur_info['item_count'][table_name] = {
                        'total': 0,
                        'pages': 0,
                        'fetch_num': 0,
                    }
                    self.cur_info['ac_tb_name'] = table_name
                    self.cur_info['ac_tb_total'] = 0
                    self.cur_info['ac_tb_pbar'] = None
                    self.fetch_ad_report(profile_id=profile_id, url=url, table_name=table_name)
                    # time.sleep(1)
                ac_json_data_str = json.dumps(self.cur_info['item_count']) if self.cur_info['total_count'] else '{}'
                MS.update(f'UPDATE `data_lingxing_account` SET `item_count` = %s,`total_count` = %s,`status` = %s WHERE `profile_id` = %s',
                          (ac_json_data_str, self.cur_info['total_count'], self.cur_info['finally_status'], profile_id,))
                pin.pin(f'{store["alias"]} 结束记录')
                print()
            pbar.close()
            logging('所有目标店铺已抓取结束！')
        else:
            time.sleep(10)

    def inventory_adjustments1(self):
        json_data = {
            'offset': 0,
            'length': 50,
            'search_field': 'msku',
            'search_value': [
                'BM-Ktm-11pm-5baih',
            ],
            'exact_search': 1,
            'sids': '',
            'status': '1',
            'is_pair': '',
            'fulfillment_channel_type': '',
            'global_tag_ids': '',
            'req_time_sequence': '/listing-api/api/product/showOnline$$2',
        }
        self.sp_erp.post('https://gw.lingxingerp.com/listing-api/api/product/showOnline', json=json_data)
        if self.sp_erp.json.get('code') == 1:
            data = self.sp_erp.json.get('data').get('list')
            inventory = 88
            params = [{'store_id': x.get('store_id'), 'msku': x.get('msku'), 'fbm_inventory': inventory} for x in data]
            params = params
            json_data = {
                'params': params,
                'req_time_sequence': '/api/module/listingPublish/amazon.AdjustListing/modifyFbmInventory$$2',
            }
            print(json_data)
            self.sp_erp.post('https://muke.lingxing.com/api/module/listingPublish/amazon.AdjustListing/modifyFbmInventory', json=json_data)
            # self.sp_erp.json
            if self.sp_erp.json.get('code') == 1:
                failure_num = self.sp_erp.json.get('data').get('failure_num')
                success_num = self.sp_erp.json.get('data').get('success_num')
                print(f'成功修改 {success_num} 个，失败 {failure_num} 个')
                if failure_num:
                    for item in self.sp_erp.json.get('data').get('failure_detail'):
                        print(item)
            else:
                print(f'修改失败: {self.sp_erp.json}')
        else:
            print('获取数据失败')
            print(self.sp_erp.json)

    def search_and_prepare_params(self, search_field, search_value, new_qty, store=''):
        """
        获取产品当前库存信息并构建修改库存的请求参数。

        参数:
        search_field (str): 查询字段名称。
        search_value (str): 查询字段值。
        new_qty (int): 新的库存数量。
        store (str): 店铺名称，可选参数，默认为空字符串。

        返回:
        list: 包含修改库存所需参数的列表。
        """
        try:
            # 将店铺名称字符串转换为列表
            stores = split_get_list(store)

            # 构建请求参数
            json_data = {
                'offset': 0,
                'length': 200,
                'search_field': search_field,
                'search_value': search_value,
                'exact_search': 1,
                'sids': '',
                'status': '1',
                'is_pair': '',
                'fulfillment_channel_type': '',
                'global_tag_ids': '',
                'req_time_sequence': '/listing-api/api/product/showOnline$$2',
            }

            # 发送请求
            self.sp_erp.post('https://gw.lingxingerp.com/listing-api/api/product/showOnline', json=json_data)

            # 解析响应数据
            data_json = self.sp_erp.json
            if data_json.get('code') != 1:
                raise Exception(f'查询{search_field}={search_value}库存数据失败')

            products = data_json.get('data', {}).get('list', [])
            params = [
                {'store_id': item.get('store_id'), 'msku': item.get('msku'), 'fbm_inventory': new_qty}
                for item in products
                if not stores or item.get('seller_name') in stores
            ]

            if not params:
                raise Exception(f'没有找到{search_field}={search_value}的库存数据')

            return params

        except Exception as e:
            print(f"{search_field}={search_value}获取和修改库存参数时出错: {e}")
            raise e

    def update_inventory(self, params):
        """
        发送请求修改库存
        """
        try:
            json_data = {
                'params': params,
                'req_time_sequence': '/api/module/listingPublish/amazon.AdjustListing/modifyFbmInventory$$2',
            }
            self.sp_erp.post('https://muke.lingxing.com/api/module/listingPublish/amazon.AdjustListing/modifyFbmInventory', json=json_data)
            data_json = self.sp_erp.json
            if data_json.get('code') != 1:
                raise Exception(f'修改失败: {data_json}')
            failure_num = data_json.get('data').get('failure_num')
            success_num = data_json.get('data').get('success_num')
            result = f'成功修改 {success_num} 个，失败 {failure_num} 个'
            if failure_num:
                err_msg = data_json.get('data').get('failure_detail')
                return err_msg
            else:
                return result
        except Exception as e:
            print(f"修改库存时出错: {e}")
            raise e

    def inventory_adjustments(self):
        try:
            search_field = 'msku'
            search_value = ['BM-Ktm-11pm-5baih']
            stores = 'AM_PN-DE、AM_PN-IT'
            params = self.search_and_prepare_params(search_field, search_value, 89, stores)
            result = self.update_inventory(params)
            logging(f'修改库存结果: {result}')
        except Exception as e:
            print(f"任务流程出错: {e}")
            raise e

    def get_report_download_center(self):
        # 设置保存路径
        save_path = r'D:\rpa_download\excel\create_report'
        self.sp_ad.set.download_path(save_path)
        errors = []  # 用于收集异常信息

        # 固定请求头
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://ads.lingxing.com',
            'Referer': 'https://ads.lingxing.com/ak_download/download_center/download_report_log/create_report',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0',
        }

        # 生成指定日期报告列表请求参数
        def prepare_report_data(created_at):
            return {
                'draw': '3',
                'columns[0][data]': 'created_at',
                'columns[0][name]': '',
                'columns[0][searchable]': 'true',
                'columns[0][orderable]': 'true',
                'columns[0][search][value]': '',
                'columns[0][search][regex]': 'false',
                'order[0][column]': '0',
                'order[0][dir]': 'desc',
                'start': '0',
                'length': '250',
                'search[value]': '',
                'search[regex]': 'false',
                'profile_id': '1278841594360273',
                'created_at': created_at,
                'page': '1',
            }

        # 获取报告参数
        def get_report_res_datas(created_at):
            self.check_ad_user()
            report_data = prepare_report_data(created_at)
            self.sp_ad.set.headers({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            })
            try:
                self.sp_ad.post('https://ads.lingxing.com/ak_download/download_center/download_report_log/list', data=report_data)
                if self.sp_ad.json.get('successful'):
                    return self.sp_ad.json.get('data', [])
                else:
                    error_message = f'获取报告参数失败: {self.sp_ad.json}'
                    errors.append(error_message)
                    return []
            except Exception as e:
                error_message = f'请求报告参数时发生错误: {e}'
                errors.append(error_message)
                return []

        # 获取所有店铺
        def get_profile_list():
            self.sp_ad.set.headers({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            })
            try:
                data = {
                    "is_with_report_date": "0"
                }
                self.sp_ad.post('https://ads.lingxing.com/common/common_list/common_list/get_profile_list', data=data)
                if self.sp_ad.json.get('successful'):
                    data_list = self.sp_ad.json.get('data', [])
                    ids = [item.get('profile_id') for item in data_list]
                    return ids
                else:
                    error_message = f'获取报告参数失败: {self.sp_ad.json["message"]}'
                    errors.append(error_message)
                    return []
            except Exception as e:
                error_message = f'请求报告参数时发生错误: {e}'
                errors.append(error_message)
                return []

        # 生成报告
        def generate_reports(reports):
            logging(f'待生成的报告数量为: {len(reports)}份')
            profile_ids = get_profile_list()
            for report in reports:
                all_param = report.get('query_condition', {}).get('allParam', {})
                report_name = all_param.get("report_name")
                report_date = calculate_date_range(report_name)

                create_param = {
                    'profile_id': all_param.get('profile_id'),
                    'profile_ids': all_param.get('profile_ids', []) if all_param.get('ads_object') == 'keyword' and convert_to_int(report_name) > 7 else profile_ids,
                    'reports': [{
                        'ads_object': all_param.get('ads_object'),
                        'ads_type': all_param.get('ads_type'),
                        'column': all_param.get('column'),
                        'export_index': all_param.get('export_index'),
                        'export_type': all_param.get('export_type'),
                        'report_date': report_date,
                        'report_name': report_name,
                    }],
                }

                try:
                    self.sp_ad.post('https://ads.lingxing.com/ak_download/download_center/index/batch_create_report', headers=headers, json=create_param)
                    if self.sp_ad.json.get('successful') and not self.sp_ad.json.get('data').get('error'):
                        logging(f'报告 {report_name} 已添加生成队列')
                    elif '1分钟内相同参数请勿重复导出' not in str(self.sp_ad.json.get('data').get('message')):
                        logging(f'报告 {report_name} 提示重复生成')
                    else:
                        error_message = f'报告{report_name}生成失败: {self.sp_ad.json.get("data").get("error")}'
                        errors.append(error_message)
                except Exception as e:
                    error_message = f'生成报告时发生错误: {e}'
                    errors.append(error_message)

        # 下载报告
        def download_reports(date):
            start_time = time.time()

            while True:
                # 检查超时
                if time.time() - start_time > 7200:  # 1小时
                    error_message = '检测报告超时，下载失败'
                    errors.append(error_message)
                    logging(error_message)
                    return False

                # 检查当天的所有报告状态
                report_res_datas = get_report_res_datas(date)
                if not report_res_datas:
                    error_message = '当天报告未生成！'
                    errors.append(error_message)
                    logging(error_message)
                    return False
                # 计算 report_status 不等于 2（已完成） 或 -2（删除） 的报告数量
                incomplete_reports_count = sum(
                        1 for report in report_res_datas if dateutil.parser.parse(report['created_at']) > create_time and report['report_status'] not in [2, -2])

                if incomplete_reports_count:
                    logging(f"{incomplete_reports_count}份报告仍在生成中，等待1分钟后重试...")
                    time.sleep(60)
                    continue

                logging("所有报告已生成完毕，开始下载报告。")

                report_res_datas = [report for report in report_res_datas if report['report_status'] == 2]
                for report in report_res_datas:
                    report_id = report.get('report_id')
                    report_name = report.get('report_name')
                    try:
                        self.check_ad_user()
                        data = {'report_id': report_id}
                        self.sp_ad.post('https://ads.lingxing.com/ak_download/download_center/index/downloadResource', json=data)

                        if self.sp_ad.json.get('successful'):
                            sign_url = self.sp_ad.json.get('data', {}).get('signUrl')
                            if sign_url:
                                self.sp_ad.download.add(sign_url, rename=report_name, file_exists='overwrite')
                        else:
                            error_message = f'下载报告{report_name}失败: {self.sp_ad.json}'
                            errors.append(error_message)
                            logging(error_message)

                    except Exception as e:
                        error_message = f'请求下载报告{report_name}时发生错误: {e}'
                        errors.append(error_message)
                        logging(error_message)

                # 等待所有下载任务完成
                self.sp_ad.download.wait()
                failed_missions = self.sp_ad.download.get_failed_missions()

                if failed_missions:
                    err = f'广告报告下载失败，请重试，失败信息：{failed_missions}'
                    logging(err)
                    errors.append(err)
                else:
                    logging(f'广告报告下载完毕,储存路径 {save_path}, 共 {len(report_res_datas)} 个文件')
                return True  # 下载完成后跳出循环

        self.check_ad_user()

        # 获取并处理报告参数
        model_created_at = '2024-09-12 - 2024-09-12'
        model_created_at = '2024-11-21 - 2024-11-21'
        cur_day = datetime.today().strftime('%Y-%m-%d')
        today = f'{cur_day} - {cur_day}'
        logging(f'正在处理{cur_day}的报告。。。')
        model_report_res_datas = get_report_res_datas(model_created_at)
        model_report_res_datas = [report for report in model_report_res_datas if report['report_name'] != '广告活动报告-前一天-每日明细' and report['report_status'] == 2]
        today_report_res_datas = get_report_res_datas(today)

        create_at = dateutil.parser.parse('09:00:00')

        # 找出待生成的报告
        missing_reports = []
        if today_report_res_datas:
            logging('正在检测需生成的报告。。。')
            today_report_datas = {report['report_name']: report for report in today_report_res_datas}
            previous_report_datas = {report['report_name']: report for report in model_report_res_datas}

            for report_name, report in previous_report_datas.items():
                if report_name == '广告活动报告-前一天-每日明细' or report.get('report_status') != 2:
                    continue
                elif report_name not in today_report_datas or (
                        today_report_datas.get(report_name).get('report_status') != 2 and dateutil.parser.parse(report.get('created_at')) > create_at):
                    missing_reports.append(report)
        else:
            logging('未检测到今日报告，需重新生成当天的报告。。。')
            missing_reports = model_report_res_datas

        create_time = datetime.now()  # 本次生成时间
        create_time = dateutil.parser.parse('09:00:00')
        # 生成尚未生成的报告
        if missing_reports: generate_reports(missing_reports)

        # 下载当天报告
        logging('正在下载当天报告。。。')
        down_result = download_reports(today)

        # 解析表格；存数据库
        try:
            if down_result:
                excel_to_sql(save_path)
        except Exception as e:
            error_message = f'解析表格时发生错误: {e}'
            errors.append(error_message)

        # 统一输出异常信息
        return '| '.join(errors) if errors else '任务执行完毕'

    def get_report_product_performance(self, export_param_data=None):
        if not export_param_data:
            date_range = get_last_n_days(7)
            export_param_data = {
                'start_date': min(date_range),
                'end_date': max(date_range),
            }
        # 设置保存路径
        save_path = r'D:\rpa_download\excel\report_file'
        self.sp_erp.set.download_path(save_path)
        errors = []  # 用于收集异常信息
        today = get_today(format='%Y-%m-%d')
        tb_task_name = self.task_params.get('tb_task_name') or 'task_lingxing'

        def get_report_res_datas():
            try:
                url = "https://muke.lingxing.com/api/download/downloadCenterReport/getReportData"
                params = {
                    "offset": "0",
                    "length": "20",
                    "report_time_type": "0",
                    "start_time": today,
                    "end_time": today,
                    "req_time_sequence": "/api/download/downloadCenterReport/getReportData$$1"
                }
                self.sp_erp.get(url, params=params)
                if self.sp_erp.json.get('code') == 1:
                    return self.sp_erp.json.get('data', {}).get('list') or []
                else:
                    error_message = f'获取报告参数失败: {self.sp_erp.json}'
                    errors.append(error_message)
                    return []
            except Exception as e:
                error_message = f'请求报告参数时发生错误: {e}'
                errors.append(error_message)
                return []

        def export_product_performance():
            try:
                logging('正在检索生成报告...')
                data = {
                    'sort_field': 'volume',
                    'sort_type': 'desc',
                    'offset': 0,
                    'length': 20,
                    'search_field': 'asin',
                    'search_value': [],
                    'mids': '',
                    'sids': '',
                    'date_type': 'purchase',
                    'start_date': today,
                    'end_date': today,
                    'principal_uids': [],
                    'bids': [],
                    'cids': [],
                    'extend_search': [],
                    'summary_field': 'msku',
                    'purchase_status': 0,
                    'currency_code': '',
                    'product_states': [],
                    'is_resale': '',
                    'order_types': [],
                    'promotions': [],
                    'developers': [],
                    'delivery_methods': [],
                    'is_recently_enum': False,  # 仅显示近180天内创建或近365天有销量的商品
                    'ad_cost_type': '',
                    'attr_value_ids': [],
                    'turn_on_summary': 0,
                    'summary_field_level1': '',
                    'summary_field_level2': '',
                    'gtag_ids': [],
                    'sub_summary_type': 'msku',
                    'regions': [],
                    'date_range_type': 0,
                    'only_query_today': False,
                }
                download_params = {
                    "total": 0,
                    "download_display": 1,
                    "export_fields": "rdate,parent_asin,asin,msku,local_name,model,local_sku,spu_name,spu,tags,item_name,mid,sid,bid,cid,principal_names,developer_names,product_create_time,landed_price,volume,amount,order_items,volume_chain_ratio,amount_chain_ratio,order_chain_ratio,volume_yoy_ratio,amount_yoy_ratio,order_yoy_ratio,net_amount,b2b_volume,b2b_amount,b2b_order_items,promotion_volume,promotion_amount,promotion_order_items,avg_custom_price,avg_volume,promotion_discount,fbm_buyer_expenses,cate_rank,small_cate_rank,return_count,return_amount,return_rate,avg_star,reviews_count,gross_profit,predict_gross_profit,gross_margin,predict_gross_margin,roi,return_goods_count,return_goods_rate,afn_fulfillable_quantity,available_inventory,fbm_quantity,available_days,fbm_available_days,oversea_quantity,local_quantity,purchase_num,month_stock_sales_ratio,out_stock_date,reserved_fc_transfers,reserved_fc_processing,afn_inbound_receiving_quantity,afn_total_inbound,reserved_customerorders,afn_inbound_shipped_quantity,afn_inbound_working_quantity,afn_unsellable_quantity,cpc,ctr,spend,roas,acos,acoas,asoas,cpo,cpu,cpm,ad_sales_amount,ad_order_quantity,ad_direct_order_quantity,ad_direct_sales_amount,adv_rate,ad_cvr,impressions,clicks",
                    "req_time_sequence": "/bd/productPerformance/download$$1"
                }
                data.update(download_params)
                if export_param_data:
                    data.update(export_param_data)
                    logging(f'正在使用自定义参数：{export_param_data}')
                url = "https://gw.lingxingerp.com/bd/productPerformance/download"
                for _ in range(3):
                    rs = self.sess_erp.post(url, json=data)
                    json_data = rs.json() or {}
                    code = json_data.get('code')
                    msg = json_data.get('msg')
                    if code == 1:
                        logging('导出产品表现报表成功,请稍后到下载中心继续操作！')
                        return True
                    elif '高峰期' in msg:
                        logging(msg)
                        errors.append(msg)
                        # time.sleep(60)
                        # continue
                        return False
                    elif msg == '查询异常':
                        logging(f'查询异常，当前时间段可能未生成数据！err:{json_data}')
                        return False
                    else:
                        error_message = f'导出产品表现报表失败: {json_data} --cookies_erp:【{self.cookies_erp}】 ---sess_ad.auth-token:【{self.sess_erp.headers.get("auth-token")}】'
                        errors.append(error_message)
                        return []
            except Exception as e:
                error_message = f'导出产品表现报表发生错误: {e}'
                errors.append(error_message)
                return []

        def download_reports():
            start_time = time.time()
            file_full_path = ''
            while True:
                # 检查超时
                if time.time() - start_time > 3600:  # 1小时
                    error_message = '检测报告超时，下载失败'
                    errors.append(error_message)
                    print(error_message)
                    return False
                # 检查当天的所有报告状态
                report_res_datas = get_report_res_datas()
                tag_reports = [report for report in report_res_datas if '产品表现MSKU' in report['report_name']]

                if tag_reports and tag_reports[0]['report_status'] != 2:
                    sleep = 60
                    logging(f"产品表现MSKU报告仍在生成中，等待{sleep}秒后重试...")
                    time.sleep(sleep)
                    continue

                logging("报告已生成完毕，开始下载报告。")

                final_data = tag_reports[:1]
                for report in final_data:
                    report_id = report.get('report_id')
                    report_name = report.get('report_name')
                    file_full_path = os.path.join(save_path, f'{report_name}.xlsx')
                    down_result = self.down_excel(report_id, report_name, save_path)
                    if down_result is True:
                        logging(f'{report_name} 下载成功\n报告创建时间：{report["gmt_create"]}\n生成时间：{report["gmt_modified"]}')
                    else:
                        error_message = down_result
                        errors.append(error_message)
                        logging(error_message)

                return file_full_path  # 下载完成后跳出循环

        status, result, platform_num, task_num, done_time, task_id = 20, '未执行', 0, 0, 0, 0
        self.check_erp_user()
        if export_product_performance():  # 生成报表
            file = download_reports()  # 下载报表
            if file and self.is_into_db_product_performance:
                result = insert_xlsx_into_count_goods(file, self.task_params)
                if result is not True: errors.append(result)
            task_id = self.task_params.get('task_id')
            done_time = now_int()
            platform_num = self.task_params.get('platform_num') or 0
            task_num = self.task_params.get('task_num') or 0
            result = '完成'
            status = 10
        if errors:
            result = '| '.join(errors)
            status = 20
        MS.update(
                f'UPDATE `{tb_task_name}` SET status = %s, result = %s, platform_num = %s, task_num = %s,`done_time` = %s WHERE id = %s ',
                (status, result, platform_num, task_num, done_time, task_id)
        )
        logging(f"任务执行完毕,本次操作数据{task_num}条。")

        # 统一输出异常信息
        return result

    def api_get_sellers(self):
        self.check_erp_user()
        self.sp_erp.get('https://muke.lingxing.com/api/my/sellers?req_time_sequence=%2Fapi%2Fmy%2Fsellers$$1', )
        if self.sp_erp.json.get('code') != 1:
            logging(f'api获取卖家列表失败')
            return []
        return self.sp_erp.json.get('list') or []

    def run_task_get_report_product_performance(self, fetch_dates=None):
        fetch_dates = fetch_dates or get_last_n_days(7)
        # sellers = self.api_get_sellers()
        # sids = [s['id'] for s in sellers]
        #
        # number_of_groups = 50
        # sids_groups = [sids[i:i + number_of_groups] for i in range(0, len(sids), number_of_groups)]
        #
        # export_param_data = {
        #     "start_date": min(fetch_dates),
        #     "end_date": max(fetch_dates),
        # }
        # for group in tqdm(sids_groups[1:], desc='任务进度'):
        #     export_param_data['sids'] = ','.join(map(str, group))
        #     result = self.get_report_product_performance(export_param_data)
        #     logging(result)

        export_param_data = {
            # "start_date": min(fetch_dates),
            # "end_date": max(fetch_dates),
            'start_date': '2024-11-11',
            'end_date': '2024-11-11',
            # 'extend_search': [
            #     {
            #         "field": "gross_profit",
            #         "from_value": "0",
            #         "to_value": "",
            #         "exp": "ne"
            #     }
            # ]
        }
        result = self.get_report_product_performance(export_param_data)

    def down_excel(self, report_id, report_name, save_path=None):
        save_path = save_path or r'D:\rpa_download\excel\report_file'
        data = {'report_id': report_id}
        try:
            self.check_ad_user()
            rs = self.sess_erp.get('https://muke.lingxing.com/api/download/downloadCenterReport/downloadResource', params=data)
            if rs.status_code == 200:
                file_full_path = os.path.join(save_path, f'{report_name}.xlsx')
                with open(file_full_path, 'wb') as f:
                    f.write(rs.content)
                return True
            else:
                error_message = f'下载报告{report_name}失败: {rs.text}'
        except Exception as e:
            error_message = f'请求下载报告{report_name}时发生错误: {e}'
        return error_message

    def fetch_ad_metrics(self, **kwargs):
        errors = []
        if not kwargs.get('no_check_user'):
            self.check_ad_user()
            self.sp_ad.set.headers({
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            })
        # 需要检查的参数列表
        required_params = 'alias, profile_id, ad_type'.split(',')
        # 检查参数并赋值
        params = {}
        for param in required_params:
            param = param.strip()
            value = kwargs.get(param)
            if not value:
                raise ValueError(f"参数 '{param}' 不能为空")
            params[param] = value
            # 解包参数
        alias, profile_id, ad_type = params.values()
        report_date = kwargs.get('report_date') or get_date_range_str(0)

        try:
            data = {
                'profile_id': profile_id,
                'is_daily': '1',
                'date_key': 'day',
                'record_key': 'total',
                'report_date': report_date,
                'only_over_budget': '0',
                'with_ring': '0',
            }
            url = 'https://ads.lingxing.com/ad_report/headline/index/detail?ajax' if ad_type == 'sb' else 'https://ads.lingxing.com/ad_report/campaign/index/detail?ajax'
            self.sp_ad.post(url, data=data)
            json_data = self.sp_ad.json
            if json_data.get('successful'):
                data_list = json_data.get('daily', [])
                save_result = insert_into_data_lingxing_ad_metrics(data_list, kwargs)
                # time.sleep(1)
                if save_result is not True:
                    errors.append(save_result)
            else:
                error_message = f'获取报告参数失败: {json_data["message"]}'
                errors.append(error_message)
        except Exception as e:
            error_message = f'请求报告参数时发生错误: {e}'
            errors.append(error_message)

        # if not errors:
        #     logging(f"任务执行完毕。")
        # else:
        #     logging(f"任务执行完毕，但存在错误。错误信息如下：\n{errors}")
        return errors

    def fetch_ad_analysis(self, task_param=None):
        errors = []
        task_param = task_param or {}
        self.check_ad_user()
        self.sp_ad.set.headers({
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        })
        url_adaggregate = 'https://ads.lingxing.com/ad_report/analyze/company/adaggregate'
        profile_id = '1639255035128355'
        days = get_last_n_days(7)

        param_data = {
            'profile_id': profile_id,
            'sponsored_types[]': [
                'sp',
                'hsa',
                'sd',
            ],
            'with_ring': '0',
            'rate_type': 'latest',
            'currency_code': 'USD',
        }
        param_data_index = {
            **param_data,
            'draw': '1',
            'start': '0',
            'length': '-1',
            'report_date': f'{days[0]} - {days[0]}',
            'page': '1',
        }

        try:
            datas = []
            for day_index in tqdm(days, desc='获取全局概览数据'):
                param_data_index['report_date'] = f'{day_index} - {day_index}'
                self.sp_ad.post('https://ads.lingxing.com/ad_report/analyze/company/index', data=param_data_index)
                json_data = self.sp_ad.json or {}
                if json_data.get('successful'):
                    data_list = json_data.get('data', [])
                    if data_list:
                        for item in data_list:
                            if not item['store_id']:
                                data_list.remove(item)
                            item['store'] = item['group_by']
                        datas.extend(data_list)
                else:
                    error_message = f'获取报告参数失败: {json_data["message"]}'
                    errors.append(error_message)
                task_param['datetime'] = day_index
                # logging(f'正在保存{day_index} 数量：{len(datas)}')
                save_result = insert_into_data_lingxing_ad_analyze(datas, task_param)
                if save_result is not True: errors.append(save_result)
        except Exception as e:
            error_message = f'请求报告参数时发生错误: {e}'
            errors.append(error_message)

        if not errors:
            logging(f"任务执行完毕。")
        else:
            err = f"任务执行完毕，但存在错误。错误信息如下：\n{errors}"
            logging(err)
            fsmsg.send(content=err)
        return errors

    def fetch_ad_analysis_old(self, task_param=None):
        errors = []
        task_param = task_param or {}
        self.check_ad_user()
        self.sp_ad.set.headers({
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        })
        url_adaggregate = 'https://ads.lingxing.com/ad_report/analyze/company/adaggregate'
        profile_id = '1639255035128355'
        days = get_last_n_days(7)

        param_data = {
            'profile_id': profile_id,
            'sponsored_types[]': [
                'sp',
                'hsa',
                'sd',
            ],
            'with_ring': '0',
            'rate_type': 'latest',
            'currency_code': 'USD',
        }
        param_data_index = {
            **param_data,
            'draw': '1',
            'start': '0',
            'length': '-1',
            'report_date': f'{days[0]} - {days[0]}',
            'page': '1',
        }

        self.sp_ad.post('https://ads.lingxing.com/ad_report/analyze/company/index', data=param_data_index)
        json_data = self.sp_ad.json or {}
        try:
            result_data = json_data.get('data') or []
            for day_index in days:
                datas = []
                task_param['datetime'] = day_index
                for row in tqdm(result_data, desc='获取全局概览数据', postfix={'日期': day_index}):
                    store_id = row.get('store_id')
                    if not store_id:
                        continue
                    param_data_ad = {
                        **param_data,
                        'report_date': f'{day_index} - {day_index}',
                        'store_id': store_id,
                    }
                    self.sp_ad.post(url_adaggregate, data=param_data_ad)
                    json_data = self.sp_ad.json
                    if json_data.get('successful'):
                        data_list = json_data.get('data', [])
                        if data_list:
                            for item in data_list:
                                item['store'] = row['group_by']
                            datas.extend(data_list)
                    else:
                        error_message = f'获取报告参数失败: {json_data["message"]}'
                        errors.append(error_message)
                logging(f'正在保存{day_index} 数量：{len(datas)}')
                save_result = insert_into_data_lingxing_ad_analyze(datas, task_param)
                if save_result is not True: errors.append(save_result)
        except Exception as e:
            error_message = f'请求报告参数时发生错误: {e}'
            errors.append(error_message)

        if not errors:
            logging(f"任务执行完毕。")
        else:
            err = f"任务执行完毕，但存在错误。错误信息如下：\n{errors}"
            logging(err)
            fsmsg.send(content=err)
        return errors


def insert_into_data_lingxing_ad_metrics(data, params=None):
    if not data:
        logging('没有数据')
        return
    if params is None:
        params = {}
    table_name = "data_lingxing_ad_metrics"  # 数据库中的表名
    try:
        df = pd.DataFrame(data)
        # 去除百分比,转换类型 应用该函数到每一列
        df = remove_percent_signs(df)

        task_num = len(df)
        params['platform_num'] = task_num
        params['task_num'] = task_num

        df['task_id'] = params.get('task_id') or now_int()
        df['datetime'] = convert_timestamp_column(df, 'date_day')
        df['username'] = params.get('username') or ''
        df['user_id'] = params.get('user_id') or 0
        df['data_status'] = params.get('data_status') or 1

        df['alias'] = params.get('alias')
        df['profile_id'] = params.get('profile_id')
        df['ad_type'] = params.get('ad_type')

        unique_columns = 'profile_id,datetime,ad_type'.split(',')
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, unique_columns)
        df['create_time'] = now_int()

        result = insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表
    except Exception as e:
        traceback.print_exc()
        result = f'{params.get("alias")} 插入失败，错误信息：{e}'
    return result


def insert_into_data_lingxing_ad_analyze(data, params=None):
    if not data:
        logging('没有数据')
        return
    if params is None:
        params = {}
    table_name = "data_lingxing_ad_analyze"  # 数据库中的表名
    try:
        df = pd.DataFrame(data)
        # 去除百分比,转换类型 应用该函数到每一列
        df = remove_percent_signs(df)

        task_num = len(df)
        params['platform_num'] = task_num
        params['task_num'] = task_num

        df['task_id'] = params.get('task_id') or now_int()
        df['datetime'] = convert_timestamp(params.get('datetime'))
        df['username'] = params.get('username') or ''
        df['user_id'] = params.get('user_id') or 0
        df['data_status'] = params.get('data_status') or 1

        unique_columns = 'store_id,datetime'.split(',')
        # unique_columns = 'sponsored_type,store_id,datetime'.split(',')
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, unique_columns)
        df['create_time'] = now_int()

        result = insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表
    except Exception as e:
        traceback.print_exc()
        result = f'数据存储失败，错误信息：{e}'
    return result


def insert_xlsx_into_center_report(file_path, file_id=0):
    table_name = "data_lingxing_center_report"  # 数据库中的表名
    file_name = os.path.splitext(os.path.basename(file_path))[0]
    tqdm.write(logging(f"正在处理【{file_name}】 id:{file_id}", isprint=False))
    # 获取文件名（不带后缀名）
    # MS = MysqlHelper()  # 测试库
    try:
        # 读取 xlsx 文件
        df = pd.read_excel(file_path)
        columns = df.columns.tolist()
        new_columns = [parse_column_name(col, LingxingConfig.column_targets_ads) for col in columns]
        df.columns = new_columns

        # 其他数据
        task_id = get_today_zero_timestamp()
        df['source'] = file_name
        df['source_id'] = file_id
        df['task_id'] = task_id
        df['datetime'] = get_today_zero_timestamp()
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, new_columns)
        df['create_time'] = now_int()

        insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表

        if MS.err:
            msg = logging(f"{file_name} 插入失败。{MS.err}", isprint=False)
        else:
            msg = logging(f"{file_name} 已保存。", isprint=False)
        tqdm.write(msg)
    except Exception as e:
        # traceback.print_exc()
        logging(f'{file_name} 插入失败，错误信息：{e}', 'ERROR')


def insert_xlsx_into_count_goods(file_path, params=None):
    if params is None:
        params = {}
    table_name = "data_lingxing_count_goods"  # 数据库中的表名
    file_name = os.path.splitext(os.path.basename(file_path))[0]  # 获取文件名（不带后缀名）
    logging(f"正在处理【{file_name}】")
    try:
        df = pd.read_excel(file_path)  # 读取 xlsx 文件
        df.fillna('', inplace=True)  # 处理 NaN 值

        task_num = len(df)
        params['platform_num'] = task_num
        params['task_num'] = task_num

        columns_old = df.columns.tolist()
        columns_new = [parse_column_name(col, LingxingConfig.column_targets_count) for col in columns_old]
        df.columns = columns_new

        # 处理 landed_price 应用函数并拆分结果
        df[['currency', 'landed_price', ]] = df.apply(
                lambda row: separate_currency_price(row['landed_price'], True),
                axis=1, result_type='expand'
        )
        # 去除百分比 应用该函数到每一列
        df = remove_percent_signs(df)
        # 转换日期时间字符串为 datetime 对象，并处理 NaT 值
        df['out_stock_date'] = convert_timestamp_column(df, 'out_stock_date')
        df['product_create_time'] = convert_timestamp_column(df, 'product_create_time')
        rdate = LingxingConfig.get_date_column(df.columns)
        if rdate in df.columns: df[rdate] = convert_timestamp_column(df, rdate)

        df['datetime'] = df.get(rdate) or params.get('date_time') or params.get('datetime') or get_today_zero_timestamp()
        df['task_id'] = params.get('task_id') or now_int()
        df['username'] = params.get('username') or ''
        df['user_id'] = params.get('user_id') or 0
        df['data_status'] = params.get('data_status') or 1
        unique_columns = 'asin,msku,sid,datetime'.split(',')
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, unique_columns)
        df['create_time'] = now_int()

        result = insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表
    except Exception as e:
        traceback.print_exc()
        result = f'{file_name} 插入失败，错误信息：{e}'
    return result


def excel_to_sql(path=''):
    """
    将Excel文件解压、重命名并导入到SQL数据库中。

    首先调用unzip_and_rename函数对指定的file_path路径下的Excel文件进行解压和重命名。
    然后，将这些处理后的文件从默认的保存路径或提供的file_path路径中读取，并逐个导入到MySQL数据库中。

    参数:
    - file_path (str, optional): Excel文件的路径。默认为空字符串，表示使用默认的保存路径。

    返回:
    无
    """
    # 如果没有提供file_path，则使用默认的保存路径
    save_path = path or r'D:\rpa_download\excel\create_report'
    file_map = {
        '关键词报告-30天-汇总': 7, '关键词报告-近3天-SB-汇总': 2, '关键词报告-近3天-SP-汇总': 3, '关键词报告-近7天-SB-汇总': 4, '关键词报告-近7天-SP-汇总': 6,
        '商品推广报告-前一天-汇总': 5, '商品推广报告-近30天-SB-汇总': 1, '商品推广报告-近30天-SP-汇总': 9, '广告活动报告-前一天-每日明细': 8,
        '商品推广报告-近3天-汇总-汇总': 10,
        '商品推广报告-近7天-汇总-汇总': 11,
        '广告活动报告-前一天-汇总-汇总': 12,
    }
    # 调用函数对Excel文件进行解压和重命名
    unzip_and_rename(save_path)
    # 获取所有文件列表
    files = [os.path.join(save_path, f) for f in os.listdir(save_path) if f.endswith('.xlsx')]
    # 使用tqdm显示进度条，遍历保存路径下的所有文件
    with tqdm(total=len(files), desc='文件处理进度', unit='份') as pbar_down:
        for file_path in files:
            name = os.path.splitext(os.path.basename(file_path))[0]
            file_id = file_map.get(name, 0)
            pbar_down.set_description_str(f'正在处理文件：{name}')
            # 将Excel文件导入到MySQL数据库中
            insert_xlsx_into_center_report(file_path, file_id)
            pbar_down.update(1)


def get_date_range_str(start=1, end=7):
    """
    获取指定日期范围的字符串表示。

    :param start: 起始日期，从当前日期往前推的天数，默认为1天，即昨天。
    :param end: 结束日期，从当前日期往前推的天数，默认为7天，即一周前。
    :return: 日期范围字符串，格式为 'YYYY-MM-DD - YYYY-MM-DD'。
    """
    # 获取当前日期
    current_date = datetime.now().date()

    start_date = current_date - timedelta(days=end)
    end_date = current_date - timedelta(days=start)

    # 格式化日期
    formatted_start_date = start_date.strftime('%Y-%m-%d')
    formatted_end_date = end_date.strftime('%Y-%m-%d')

    # 拼接日期范围字符串
    date_range_str = f'{formatted_start_date} - {formatted_end_date}'

    # 打印结果
    # print(date_range_str)
    return date_range_str


# 定义键名转换函数
def convert_key(key):
    return re.sub(r'(?<!^)(?=[A-Z])', '_', key).lower()


pin = Pinner()
# 定义一个数字类型字段的列表
numeric_fields = [
    'is_apply_rule', 'review_count', 'tenant_id', 'is_apply_grab', 'reserved_fc_processing', 'is_apply_time', 'orders', 'asin_match', 'is_has_image',
    'asin_review_count', 'datetime', 'ad_id_match', 'reserved_customerorders', 'kw_is_campaign_negative', 'afn_inbound_receiving_quantity', 'stars',
    'is_applied_inventory_rule', 'app_table_token', 'is_remarking', 'source', 'afn_researching_quantity', 'task_id', 'id', 'afn_unsellable_quantity', 'data_status',
    'asin_stars', 'company_id', 'is_fba', 'reserved_fc_transfers', 'afn_inbound_shipped_quantity', 'afn_fulfillable_quantity', 'task_time', 'clicks',
    'st_is_campaign_negative', 'afn_inbound_working_quantity', 'indirect_orders', 'calc_sale_days', 'kw_is_ad_group_negative', 'create_time', 'sale_days',
    'direct_units', 'ad_units', 'direct_orders', 'indirect_units', 'impressions', 'is_ad_group_apply_time', 'target_type', 'st_is_ad_group_negative', 'user_id']


def main_fetch_ad():
    acm = AccountManager(source='lingxing_user%')
    account = acm.account
    lx = LingXing(account=account)
    lx.check_ad_user()
    lx.goto_ad()
    lx.fetch_profile_list()
    lx.fetch_ad_data()
    lx.page.quit()
    acm.release_account()


def main_download_report():
    lx = LingXing(9335)
    result = lx.get_report_download_center()
    logging(result)
    if '任务执行完毕' not in result:
        fsmsg.send(None, result)


def main_inventory_adjustments():
    lx = LingXing(9335)
    lx.inventory_adjustments()


def main_fetch_ad_metrics():
    errors = []
    lx = LingXing(9335)
    if not is_test_environment():
        if MS.get_one('select case WHEN update_time> CURRENT_DATE then 0 ELSE 1 end as result from rpa.data_lingxing_account where `status`<>99  ORDER BY id desc limit 1')[0]:
            lx.fetch_profile_list()
    lx.check_ad_user()
    lx.sp_ad.set.headers({
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    })

    date_range = get_date_range_str(0)
    datas = MS.get_dict('select id,profile_id,alias from rpa.data_lingxing_account')

    with tqdm(datas, desc='广告指标采集', unit='个', total=len(datas)) as par_fetch:
        for row in par_fetch:
            row.update({'ad_type': 'sp', 'date': date_range, 'no_check_user': True})
            par_fetch.set_postfix({'店铺': row['alias'], '类型': row['ad_type'], })
            fetch_result = lx.fetch_ad_metrics(**row)
            if fetch_result: errors.append(fetch_result)
            row.update({'ad_type': 'sb'})
            par_fetch.set_postfix({'店铺': row['alias'], '类型': row['ad_type'], })
            fetch_result = lx.fetch_ad_metrics(**row)
            if fetch_result: errors.append(fetch_result)
    if not errors:
        logging(f"任务执行完毕。")
    else:
        err = f"任务执行完毕，但存在错误。错误信息如下：\n{errors}"
        logging(err)
        fsmsg.send(except_info=err)


def main_get_report_product_performance():
    lx = LingXing(9335)
    lx.task_params = {  # 更新任务表/数据表记录的字段
        'task_id': 0,
        'app_id': 10,
        'user_id': 18,
        'username': 'zc',
        'date_time': 1729612800,
        'data_status': 1,
        'tb_task_name': 'task_lingxing'
    }
    # lx.get_report_product_performance()

    fetch_dates = get_last_n_days(datetime.today().day + 1 - 11)
    fetch_dates.reverse()
    # fetch_dates = None
    lx.run_task_get_report_product_performance(fetch_dates)


def main_fetch_ad_analyze():
    lx = LingXing(9335)
    lx.fetch_ad_analysis()


# MS = MSXS if not is_test_environment() else MysqlHelper()
MS = MSXS
if __name__ == '__main__':
    # MS = MysqlHelper()
    # main_fetch_ad()
    # main_download_report()
    # main_inventory_adjustments()

    # main_get_report_product_performance()
    main_fetch_ad_metrics()
    # main_fetch_ad_analyze()

    input('输入任意键退出')
