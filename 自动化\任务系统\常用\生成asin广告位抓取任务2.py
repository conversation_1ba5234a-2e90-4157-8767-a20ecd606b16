import random

from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

from work.自动化.卖家精灵.SellerSpriteSpider import get_key_by_value


def create_task(data):
    MS.insert_many("""
    INSERT INTO `rpa`.`task_asins_loc` (`app_id`, `platform`, `site`, `title`, `contents`, `user_id`, `username`, `page`, `start_time`, `end_time`, `datetime`, `create_time`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, data)
    print(MS.err) if MS.err else print('抓取任务创建成功！')


MS = MSXS
app_id = 7  # 状态
keyswords = []
unique_data = set()
insert_data = []
insert_data_cron = ['iphone 16 pro hülle stitch', "iphone 14 hülle stitch", "iphone 16 pro hülle stitch", "iphone 14 pro max hülle stitch", "iphone 12 pro max hülle stitch",
                    "iphone 15 plus hülle stitch"]
for key in insert_data_cron:
    site = 'de'
    amazon_url_site = get_key_by_value(site)
    title = f'临时任务：{key}'
    contents = f'https://www.amazon.{amazon_url_site}/s?k={key}'
    if contents in unique_data:
        continue
    unique_data.add(contents)
    list_d = (app_id, 'amazon', site, title, contents, 18, 'zc', 3, get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int())
    insert_data.append(list_d)

print(f'共{len(insert_data)}个数据')
create_task(insert_data)
