# -*- coding:UTF-8 -*-
# @FileName  :main.py
# @Time      :2024/6/18 11:27
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.亚马逊.AmazonDP import *
from work.自动化.亚马逊.Amazon import *
from work.自动化.卖家精灵.SellerSpriteSpider import *


def main():
    # 获取主任务
    main_tasks = MS.get_all(
            'SELECT id FROM `task_goods` WHERE STATUS = 1 AND pid = 0 ORDER BY priority desc, cid desc, id asc limit 1') \
        if not is_test_environment() \
        else MSXS.get_all('SELECT id FROM `task_goods` WHERE id =34603')
    if not main_tasks:
        time.sleep(10)
        return

    for main_task in main_tasks:
        # 修改所有关联任务状态为执行中
        parent_id = main_task[0]
        if MS.update('UPDATE `task_goods` SET status = 2,`execute_time` = %s WHERE (pid = %s or id = %s) and `status` = 1', (now_int(), parent_id, parent_id)) < 1:
            logging(f'当前任务已经在执行')
            if not is_test_environment(): return False
        # 亚马逊任务处理
        sql_sel = f'SELECT id, app_id, url, user_id, username, datetime, site, page  FROM `task_goods`  WHERE pid = {parent_id}  AND app_id IN (2, 3) ORDER BY app_id LIMIT 1'
        rs = MS.get_one(sql_sel)
        if rs:
            task_id = rs[0]
            app_id = rs[1]
            url = rs[2]
            user_id = rs[3]
            username = rs[4]
            task_time = rs[5]  # 关联任务的所属时间`datetime`
            site = rs[6]
            pages = int(rs[7])
            params = {  # 更新任务表/数据表记录的字段
                'task_id': task_id,
                'app_id': app_id,
                'user_id': user_id,
                'username': username,
                'task_time': task_time,
                'date_time': get_today_zero_timestamp(),  # 当天0点时间戳
                'site': site,
                'pages': pages,
            }

            # 开始抓取任务
            logging(f'当前应用ID:{app_id},任务ID:{task_id},url:{url}')

            if app_id == 2:
                params_amazon = copy.deepcopy(params)
                # 更新任务表/数据表记录的字段
                MS.update(f'update `task_goods` set status = %s, execute_time=%s where pid = %s and app_id in (2,3)', (2, now_int(), parent_id))  # 更新任务状态执行开始时间
                result_data = amazon.get_asins_by_url_pages(url, pages)  # 抓取数据

                unique_asins = set([i_asin['asin'] for i_asin in result_data])
                platform_num = len(result_data)  # 抓取的实际数量
                task_num = len(unique_asins)  # 任务数量 去重后的数量
                data_status = 1 if task_num > 0 else 3  # 数据状态
                params_amazon['create_time'] = now_int()
                params_amazon['platform_num'] = platform_num
                params_amazon['task_num'] = task_num
                params_amazon['data_status'] = data_status
                params_amazon['done_time'] = now_int()
                params_amazon['result'] = amazon.cur_info['result']
                params_amazon['status'] = amazon.cur_info['status']
                insert_or_update_amazon(result_data, params_amazon)  # 更新数据表

                # 卖家精灵任务处理
                params_sprite = copy.deepcopy(params_amazon)
                params_sprite['app_id'] = 3
                task_id = MS.get_one(f'select id from task_goods where pid = {parent_id} and app_id={params_sprite["app_id"]}')[0]
                if task_id:
                    params_sprite['task_id'] = MS.get_one(f'select id from task_goods where pid = {parent_id} and app_id={params_sprite["app_id"]}')[0]
                    params_sprite['create_time'] = now_int()  # 更新数据表创建时间
                    insert_or_update_sprite(result_data, params_sprite)  # 更新数据表
            else:
                params_sprite = copy.deepcopy(params)
                # 更新任务表/数据表记录的字段
                MS.update(f'update `task_goods` set status = %s, execute_time=%s where id = %s', (2, now_int(), params_sprite['task_id']))  # 更新任务状态执行开始时间
                result_data = sss.run(url, pages)  # 抓取数据
                # result_data = sss.cur_info['cur_fetch_datas']  # 抓取数据
                platform_num = sss.cur_info['cur_total']  # url第一次请求返回的总数 平台数量
                task_num = len(result_data)  # 任务数量
                data_status = 1 if task_num > 0 else 3  # 数据状态
                params_sprite['create_time'] = now_int()
                params_sprite['platform_num'] = platform_num
                params_sprite['task_num'] = task_num
                params_sprite['data_status'] = data_status
                params_sprite['done_time'] = now_int()
                insert_or_update_sprite(result_data, params_sprite)  # 更新数据表

            # # 1688任务处理
            # params_1688 = copy.deepcopy(params)
            # params_1688['app_id'] = 4
            # task_ids = MS.get_one(f'select id from task_goods where pid = {parent_id} and app_id={params_1688["app_id"]}')
            # if task_ids:
            #     params_1688['task_id'] = task_ids[0]
            #     params_1688['platform_num'] = len(result_data) * 40  # 预测平台总数
            #     MS.update(f'update `task_goods` set status = %s, execute_time=%s where id = %s', (2, now_int(), params_1688['task_id']))  # 更新1688任务状态执行开始时间
            #     datas = []
            #     for index, data in enumerate(result_data):
            #         asin = data['asin']
            #         image = data['image']
            #         logging(f'当前应用ID:{params_1688["app_id"]};任务ID:{params_1688["task_id"]};进度:{index} / {task_num};asin:{asin};image:{image}')
            #         _datas = ali.search_image(image)
            #         logging(f'本次抓取数量:{len(_datas)}')
            #         datas.extend(_datas)
            #         ali.asin = asin
            #         print()
            #     params_1688['data_status'] = 1 if len(datas) > 0 else 3  # 数据状态
            #     params_1688['create_time'] = now_int()
            #     params_1688['done_time'] = now_int()
            #     insert_or_update_1688_listing(datas, params_1688)
        else:
            logging('未知抓取任务')

        # 最后更新主任务状态
        successful_num, result, platform_num, task_num = MS.get_one(f'SELECT  SUM(status) = SUM(10), result,platform_num,task_num FROM  task_goods WHERE  pid ={parent_id}')
        status = 10 if successful_num == 1 else 20
        result = result or '异常'
        MS.update(
                f'update `task_goods` set status = %s, result = %s, platform_num = %s, task_num = %s,done_time=%s where id = %s',
                (status, result, platform_num, task_num, now_int(), parent_id)
        )
        logging(f'主任务:{parent_id}已执行结束')
        print()


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    amazon = AmazonSK(proxy_type=2) if not is_test_environment() else AmazonSK(proxy_type=1, port=9444)
    sss = SellerSpriteSpider()
    # ali = Ali1688(9223)
    ali = None
    while True:
        try:
            main()
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send('选品应用', msg, err)
