# -*- coding:UTF-8 -*-
# @FileName  :DataFetcher.py
# @Time      :2025/2/14 15:00
# <AUTHOR>

from utils_mrc.pub_fun import *
from work.自动化.领星抓取.LingXingSessionPage import *
from TimePinner import Pinner
from dataclasses import dataclass, field
from typing import List, Dict, Any, Callable, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import Manager
from queue import Empty
import traceback
import json
import pandas as pd
from abc import ABC, abstractmethod


class BaseDataProvider(ABC):
    """数据提供者基类"""

    def __init__(self, session_instance=None):
        """初始化数据提供者"""
        self.session = session_instance or self.create_session()

    @abstractmethod
    def create_session(self):
        """创建会话实例"""
        pass

    @abstractmethod
    def get_total(self, params: Dict) -> int:
        """获取数据总数"""
        pass

    @abstractmethod
    def fetch_page(self, user: str, page: int, page_size: int, params: Dict) -> List[Dict]:
        """获取分页数据"""
        pass

    @abstractmethod
    def process_item(self, item: Dict) -> Dict:
        """处理单条数据"""
        pass

    @property
    @abstractmethod
    def table_name(self) -> str:
        """获取数据表名"""
        pass


@dataclass
class FetchConfig:
    """抓取配置类"""
    data_provider: BaseDataProvider  # 数据提供者
    users: List[str]  # 抓取账号列表
    page_size: int = 200  # 每页数据量
    max_retries: int = 5  # 最大重试次数
    save_data_func: Optional[Callable] = None  # 自定义保存数据方法(可选)

    # 任务参数
    task_params: Dict = field(default_factory=dict)  # 任务相关参数
    fetch_params: Dict = field(default_factory=dict)  # 抓取相关参数

    def __post_init__(self):
        if not self.save_data_func:
            self.save_data_func = self.default_save_data

    def default_save_data(self, data: List[Dict], params: Dict):
        """默认的数据保存方法"""
        if not data:
            return

        # 转换为DataFrame
        df = pd.DataFrame(data)

        # 添加任务相关字段
        df['app_id'] = params.get('app_id', 0)
        df['task_id'] = params.get('task_id', 0)
        df['datetime'] = params.get('datetime', int(time.time()))
        df['task_time'] = params.get('task_time', int(time.time()))
        df['data_status'] = params.get('data_status', 0)
        df['create_time'] = int(time.time())

        # 删除ID字段(如果存在)
        if 'id' in df.columns:
            df.drop(columns=['id'], inplace=True)

        # 执行插入
        print(f"模拟保存数据到表 {self.data_provider.table_name}，共 {len(data)} 条")
        # 实际保存数据的代码
        # insert_data_from_pd(self.data_provider.table_name, df)


class DataFetcher:
    """数据抓取器"""

    def __init__(self, config: FetchConfig):
        self.config = config

    def fetch_data(self) -> Dict:
        """执行数据抓取"""
        # [保持原有的抓取逻辑不变，但使用 data_provider 替代之前的回调函数]
        manager = Manager()
        shared_data = manager.dict({
            'success_pages': manager.list(),
            'failed_pages': manager.dict(),
            'data': manager.list(),
            'user_stats': manager.dict()
        })

        task_queue = manager.Queue()

        for _ in range(self.config.max_retries):
            try:
                total = self.config.data_provider.get_total(self.config.fetch_params)
                if not total:
                    logging('无数据需要抓取')
                    return {}
                break
            except Exception as e:
                logging(f'获取总数据量失败: {e}')
                return {}

        total_pages = (total + self.config.page_size - 1) // self.config.page_size
        logging(f'本次采集共{total}条数据, 共{total_pages}页, 本次采集账号数量为{len(self.config.users)}个')

        for page in range(1, total_pages + 1):
            task_queue.put(page)

        with ProcessPoolExecutor(max_workers=len(self.config.users)) as executor:
            futures = []

            for user in self.config.users:
                future = executor.submit(
                        self._run_fetch_task,
                        user=user,
                        task_queue=task_queue,
                        shared_data=shared_data
                )
                futures.append(future)

            for future in as_completed(futures):
                try:
                    result = future.result()
                    logging(f'任务已完成，结果为：{result}')
                except Exception as e:
                    print(f'任务失败，出现错误: {e}')

        results = {
            'data': list(shared_data['data']),
            'failed_pages': dict(shared_data['failed_pages']),
            'user_stats': dict(shared_data['user_stats'])
        }

        if results['data']:
            try:
                self.config.save_data_func(results['data'], self.config.task_params)
            except Exception as e:
                print(f'保存数据失败: {e}')

        return results

    def _run_fetch_task(self, user: str, task_queue, shared_data: Dict) -> Dict:
        """运行单个抓取任务"""
        user_stats = {'success': 0, 'failed': 0}

        while True:
            try:
                page = task_queue.get_nowait()
            except Empty:
                break

            if page in shared_data['success_pages']:
                continue

            retry_count = shared_data['failed_pages'].get(page, 0)
            if retry_count >= self.config.max_retries:
                continue

            try:
                items = self.config.data_provider.fetch_page(
                        user=user,
                        page=page,
                        page_size=self.config.page_size,
                        params=self.config.fetch_params
                )

                if items:
                    processed_items = [
                        self.config.data_provider.process_item(item)
                        for item in items
                    ]
                    for item in processed_items:
                        shared_data['data'].append(item)

                    shared_data['success_pages'].append(page)
                    shared_data['failed_pages'].pop(page, None)
                    user_stats['success'] += 1
                    logging(f'用户{user}第{page}页抓取成功,获取{len(items)}条数据')
                else:
                    raise Exception('返回数据为空')

            except Exception as e:
                retry_count = shared_data['failed_pages'].get(page, 0) + 1
                shared_data['failed_pages'][page] = retry_count

                logging(f'用户{user}第{page}页抓取失败(第{retry_count}次): {str(e)}')
                if retry_count >= self.config.max_retries:
                    user_stats['failed'] += 1
                    logging(f'第{page}页抓取失败，已超过最大重试次数，跳过')

                if retry_count < self.config.max_retries:
                    task_queue.put(page)

        shared_data['user_stats'][user] = user_stats
        return {
            'user': user,
            'success': user_stats['success'],
            'failed': user_stats['failed']
        }


class LingXingOrderProvider(BaseDataProvider):
    """领星订单数据提供者"""

    def create_session(self):
        return LingXingSessionPage()

    def get_total(self, params: Dict) -> int:
        return self.session.api_order_list(param_payload=params, only_total=True)

    def fetch_page(self, user: str, page: int, page_size: int, params: Dict) -> List[Dict]:
        self.session.user = user
        params.update({
            "pageNo": page,
            "pageSize": page_size
        })
        return self.session.api_order_list(params)

    def process_item(self, item: Dict) -> Dict:
        item['unique_id'] = item['amazonOrderId']
        return item

    @property
    def table_name(self) -> str:
        return 'rpa.data_lingxing_order'


class LingXingFBAInventoryProvider(BaseDataProvider):
    """领星库存数据提供者"""

    def create_session(self):
        return LingXingSessionPage()

    def get_total(self, params: Dict) -> int:
        return self.session.api_fba_inventory_list(param_payload=params, only_total=True)

    def fetch_page(self, user: str, page: int, page_size: int, params: Dict) -> List[Dict]:
        self.session.user = user
        params.update({
            "offset": (page - 1) * page_size,
            "length": page_size
        })
        return self.session.api_fba_inventory_list(params)

    def process_item(self, item: Dict) -> Dict:
        item['unique_id'] = str(item['unique_id'])
        return item

    @property
    def table_name(self) -> str:
        return 'idc.lingxing_fba_inventory'


# 使用示例
def fetch_test():
    # 测试库存抓取
    inventory_provider = LingXingFBAInventoryProvider()
    inventory_config = FetchConfig(
            data_provider=inventory_provider,
            users=['jszg01', 'yxyJS2'],
            task_params={
                'task_id': 1,
                'app_id': 1,
                'datetime': int(time.time()),
                'task_time': int(time.time())
            },
            fetch_params={
                'is_hide_zero_stock': 1
            }
    )

    logging("\n=== 测试库存数据抓取 ===")
    fetcher = DataFetcher(inventory_config)
    results = fetcher.fetch_data()
    print_results(results)


def print_results(results: Dict):
    """打印抓取结果"""
    print("\n抓取结果统计:")
    logging(f"总数据量: {len(results.get('data', []))}")
    print("\n账号统计:")
    for user, stats in results.get('user_stats', {}).items():
        logging(f"{user}: 成功{stats['success']}页, 失败{stats['failed']}页")
    if results.get('failed_pages'):
        print("\n失败页面:")
        for page, retries in results.get('failed_pages', {}).items():
            logging(f"页码 {page}: 重试 {retries} 次")


if __name__ == '__main__':
    fetch_test()
