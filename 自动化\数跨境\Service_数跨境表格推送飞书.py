import sys
import time
from threading import Thread, Lock
from flask import Flask, request, jsonify
from flask_cors import CORS
from collections import deque
from work.自动化.数跨境.SKJ import SKJ

app = Flask(__name__)
# CORS(app)  # 允许所有接口支持跨域访问
CORS(app, supports_credentials=True)  # 允许所有接口支持跨域访问
# CORS(app, resources={r"/start-task": {"origins": "*"}, r"/check-task-status/*": {"origins": "*"}})  # 允许所有来源对两个路由的跨域请求

# 初始化processing状态为False
processing = False
# 使用deque作为消息队列
task_message_queues = {}  # 存储每个任务ID的消息队列
# 锁，用于线程安全地操作消息队列
lock = Lock()


class CustomStdout:
    def __init__(self, task_id, original_stdout):
        self.task_id = task_id
        self.original_stdout = original_stdout

    def write(self, message):
        with lock:
            # 将消息添加到对应任务ID的消息队列中
            if self.task_id not in task_message_queues:
                task_message_queues[self.task_id] = deque()
            if message.strip():
                task_message_queues[self.task_id].append(message.strip())
        self.original_stdout.write(message)

    def flush(self):
        pass


@app.route('/start-task', methods=['POST'])
def start_task():
    if processing:
        msg = "采集任务正在进行中，请勿重复操作！数据实时更新，请前往 https://va81rsh3m92.feishu.cn/base/EKR7bRB24aZIKFszqO5cn3mCn9d 查看进度"
        # print(msg)
        return jsonify({"status": msg}), 429

    task_id = request.json.get('task_id', '1')
    # 获取 JSON 数据
    json_data = request.get_json()
    print(json_data)
    if 'fine_auth_token' in str(json_data):
        thread = Thread(target=main, args=(json_data,))
        thread.start()
        message = "任务已启动。"
        http_status = 202
    else:
        message = "非法请求，身份验证失败"
        http_status = 401
    return jsonify({"status": message, "task_id": task_id}), http_status


@app.route('/check-task-status/<task_id>', methods=['GET'])
def check_task_status(task_id):
    response = {"status": "正在等待任务状态更新..."}
    task_id = int(task_id)
    try:
        while True:  # 无限循环等待消息
            with lock:
                if task_message_queues.get(task_id, deque()):
                    # 检查队列是否有消息
                    message = task_message_queues[task_id][-1]  # 获取队列的第一个消息
                    if "执行完毕" in message:
                        # 如果消息包含"执行完毕"，清空队列
                        task_message_queues[task_id].clear()
                        response["status"] = message
                        break
                    else:
                        # 如果消息不是"执行完毕"，返回当前消息
                        response["status"] = task_message_queues[task_id].popleft()
                        break
            time.sleep(1)  # 短暂休眠以避免CPU过载
    except Exception as e:
        response["error"] = str(e)

    return jsonify(response)


def main(json_data):
    global processing
    processing = True
    task_id = json_data.get('task_id', 1)

    custom_stdout = CustomStdout(task_id, sys.stdout)
    sys.stdout = custom_stdout

    try:
        # for i in range(100):
        #     print(f'任务{task_id} 进度：{i + 1}%')
        #     time.sleep(1)
        SKJ(json_data).main()
    except Exception as e:
        print(f'任务{task_id} 执行出错：{str(e)}')
    finally:
        print(f'任务{task_id} 执行完毕 进度：100%。')
        processing = False
        sys.stdout = sys.__stdout__


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9999)
