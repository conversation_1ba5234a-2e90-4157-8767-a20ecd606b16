# -*- coding:UTF-8 -*-
# @FileName  :Ali1688.py
# @Time      :2024/6/3 19:59
# <AUTHOR>
from utils_mrc.pub_fun import calculate_md5_hash
import base64
import json
import os
import random
import execjs
from datetime import date
from bs4 import BeautifulSoup
from DrissionPage import WebPage, ChromiumOptions
from utils_mrc.MysqlHelper import *
from utils_mrc.FeiShuAPI import *
from utils_mrc.ProxyExtensionGenerator import ProxyExtensionGenerator


class Ali1688:
    def __init__(self, local_port=16888):
        """初始化DrissionPage对象并设置窗口显示"""
        self.cur_info = {}
        co = ChromiumOptions()
        co.set_local_port(local_port)
        co.remove_extensions()
        # co.use_system_user_path()
        try:  # 清除插件缓存文件
            plugin_cache_path = rf'{os.getenv("TEMP")}\DrissionPage\userData_{local_port}\Default\Secure Preferences'
            # print("插件缓存路径:", plugin_cache_path)
            os.remove(plugin_cache_path)
        except Exception:
            pass  # 如果文件不存在，忽略异常
        self.page = WebPage(chromium_options=co)
        self.page.set.window.show()
        self.page.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36'})
        self.app_key = '12574478'
        self.asin = ''
        self.navigate_to_default_url_if_needed()

    def navigate_to_default_url_if_needed(self):
        """如果当前页面URL不是目标URL，则导航到默认页面"""
        if '1688.com' not in self.page.url:
            self.page.get('https://www.1688.com/')
        else:
            self.page.refresh()

    def check_page(self):
        """处理验证码逻辑"""
        # self.page.set.window.show()
        # self.page.set.window.max()
        while '"SECDATA"' in self.page.html:
            print('出现验证码！', end=' ')
            self.page.set.window.show()
            self.page.set.window.max()
            time.sleep(random.uniform(0.8, 1.5))
            self.page.wait.load_start()
            try:
                ele1 = self.page('#nc_1_n1z')
                if ele1:
                    # 定义总拖动长度范围和阶段数
                    total_length_min = 300
                    total_length_max = 320
                    stages = random.randint(3, 4)  # 阶段数随机*次
                    deviation = random.randint(5, 10)

                    # 计算每阶段的基础偏移量，确保总长度随机
                    total_length = random.randint(total_length_min, total_length_max)
                    base_offset_x = total_length // stages

                    # 保存每阶段的额外偏移量以模拟“记忆”效果
                    extra_offset_x_list = []
                    offset_x = 0

                    ac_ele1 = self.page.actions.click(ele1).hold(ele1)
                    for stage in range(stages):
                        # 非线性速度变化
                        speed_factor = min(max(stage / (stages - 1), 0.2), 0.8)
                        duration = random.uniform(0.8 * speed_factor, 2.5 * speed_factor)

                        # 更精细的随机偏移
                        if stage > 0:
                            last_extra = extra_offset_x_list[-1]
                            extra_offset_x = random.randint(max(-deviation + last_extra, -10), min(deviation + last_extra, 10))
                        else:
                            extra_offset_x = random.randint(-deviation, deviation)
                        extra_offset_x_list.append(extra_offset_x)

                        current_offset_x = base_offset_x + extra_offset_x
                        offset_x += current_offset_x

                        # 考虑边界效应，最后一阶段减少偏差
                        if stage == stages - 1:
                            deviation = random.randint(1, 3)
                            offset_y = random.randint(-2, 2)
                        else:
                            offset_y = random.randint(-3, 4)

                        # 执行拖动
                        # ele1.drag(current_offset_x, offset_y, duration)
                        # self.page.actions.click(ele1).hold(ele1).move(current_offset_x, offset_y, duration)
                        ac_ele1.move(current_offset_x, offset_y, duration)
                    time.sleep(random.uniform(0.8, 1.5))  # 结束后的随机延迟
                if 'nc_1_refresh1' in self.page.html:
                    # 刷新验证码逻辑
                    # refresh_ele = self.page('#`nc_1_refresh1`', timeout=2)
                    # if refresh_ele:
                    #     print('验证失败，需要刷新验证码！')
                    #     refresh_ele.click()
                    self.page.refresh()
                    time.sleep(1)
                    self.page.wait.load_start()
                elif 'SECDATA' in self.page.html:
                    print('验证码未成功，刷新页面')
                    self.page.refresh()
                    time.sleep(1)
                    self.page.wait.load_start()
                else:
                    print('验证码成功')
                    time.sleep(2)
                    break
            except Exception as e:
                print(e)
                self.page.refresh()
                time.sleep(random.uniform(0.5, 1.2))
                self.page.wait.load_start()

        # 检查是否已登录
        while 'login.taobao' in self.page.url:
            msg = '请在完成登录后再点击确认关闭提示'
            # fsmsg.send('1688数据采集', msg, '警告')
            pg.alert(msg, '警告')
            if 'login.taobao' not in self.page.url:
                break
        if 'static/wrongpage' in self.page.url:
            # logging('404页面')
            self.cur_info['err'] = '404页面'
            return False
        elif '商品已下架' in self.page.html:
            self.cur_info['err'] = '商品已下架'
            return False

        self.cur_info['err'] = ''
        return True

    def setup_session(self):
        """设置会话信息"""
        self.page.cookies_to_session()
        self.page.change_mode('s')
        self.page.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36'})

    def display_info(self):
        """展示cookies、session cookies、headers和页面标题"""
        # print(self.page.cookies())
        # print(self.page.session.cookies)
        # print(self.page.session.headers)
        # print(self.page.title)
        rs = self.page.session.get('https://detail.1688.com/offer/786588960234.html')
        data = parse_detail(rs.text)
        print(data)
        # self.page.change_mode('d')

    def parse_list_by_js(self):
        datas = []
        for _ in range(5):
            try:
                self.page.wait.load_start()
                offerresultData = self.page.run_js('window.data.offerresultData', as_expr=True)
                if not offerresultData:
                    self.page.refresh()
                    print('offerresultData is None')
                else:
                    break
            except Exception as e:
                print(e)
                self.page.refresh()
                time.sleep(random.uniform(0.5, 1.2))
        else:
            return datas
        # print(offerresultData)
        offerList = offerresultData.get('offerList')
        for offer in offerList:
            id = offer.get('id', 0)
            image = offer.get('image', {}).get('imgUrl')
            factory = offer.get('company', {}).get('name')
            url = f'https://detail.1688.com/offer/{id}.html'
            detailUrl = offer.get('information', {}).get('detailUrl')  # https://dj.1688.com 开头多半是广告
            price = offer.get('tradePrice', {}).get('offerPrice', {}).get('priceInfo', {}).get('price')
            price_json = offer.get('tradePrice', {}).get('offerPrice', {}).get('quantityPrices', [])
            if price_json:
                price = price_json[0].get('valueString', price)
            price_json = json.dumps(price_json, ensure_ascii=False)
            sales = offer.get('tradeQuantity', {}).get('gmvValue', {}).get('integer', '')
            datas.append({
                'factory': factory,
                'sales': sales,
                'price': price,
                'price_json': price_json,
                'url': url,
                'image': image,
                'asin': self.asin,
            })
        # print(datas)
        return datas

    def parse_detail_by_js(self):
        datas = {}
        data = self.page.run_js('window.__INIT_DATA', as_expr=True)
        if not data:
            self.cur_info['data_status'] = '1688detail页面解析失败，非正常页面'
            return datas
        self.cur_info['data_status'] = ''
        # print(data)
        tempModel = data['globalData']['tempModel']
        offerBaseInfo = data['globalData']['offerBaseInfo']
        skuRangePrices = data['globalData']['orderParamModel']['orderParam']['skuParam']['skuRangePrices']
        # print(tempModel)
        # print(offerBaseInfo)
        # print(skuRangePrices)
        title = tempModel["offerTitle"]
        seller_url = f'https://detail.1688.com/offer/{tempModel["offerId"]}.html'
        seller = offerBaseInfo["sellerLoginId"]
        price = skuRangePrices

        # print(f'标题：{title}')
        # print(f'链接：{seller_url}')
        # print(f'供应商：{seller}')
        # print(f'价格：{price}')
        images = [item.get('fullPathImageURI') for item in data['globalData']['images']]
        datas['images'] = images
        datas['image'] = images[0]
        datas['title'] = title
        datas['seller_url'] = seller_url
        datas['seller'] = seller
        datas['price'] = price
        # print(datas)
        return datas

    def get_token(self):
        token = ''
        cookies = self.page.cookies(all_domains=True)
        for cookie in cookies:
            if '1688.com' in cookie['domain'] and cookie['name'] == '_m_h5_tk':
                token = cookie['value'].split('_')[0]
                break
            elif '1688.com' in cookie['domain'] and cookie['name'].startswith('_m_h5_'):
                token = cookie['value'].split('_')[0]
            elif cookie['name'] == '_m_h5_tk':
                token = cookie['value'].split('_')[0]
        else:
            print('没有找到_m_h5_前缀token！')
        if not token:
            # self.navigate_to_default_url_if_needed()
            self.page.new_tab('https://www.1688.com/')
            self.page.wait.load_start()
            time.sleep(2)
            self.get_token()

        # print(token)
        return token

    def get_data(self, file_or_url_base64):
        if file_or_url_base64.startswith('http'):
            # 获取imageBase64
            for _ in range(5):
                try:
                    rs = self.page.session.get(file_or_url_base64)
                    imageBase64 = base64.b64encode(rs.content).decode()
                    break
                except Exception as e:
                    print(e)
                    self.page.refresh()
                    time.sleep(random.uniform(0.5, 1.2))
        elif file_or_url_base64.startswith('/'):
            # 获取imageBase64
            imageBase64 = file_or_url_base64
        else:
            with open(file_or_url_base64, 'rb') as f:
                imageBase64 = base64.b64encode(f.read()).decode()
        data_json = {
            "imageBase64": imageBase64,
            "appName": "searchImageUpload",
            "appKey": "pvvljh1grxcmaay2vgpe9nb68gg9ueg2",
        }
        data_str = json.dumps(data_json, separators=(",", ":"))
        data = {"data": data_str}
        return data

    def get_sign(self, data, t, token):
        c = '&'.join([token, t, self.app_key, data.get('data', '')])
        # sign = hashlib.md5(c.encode('utf-8')).hexdigest()
        sign = calculate_md5_hash(c)
        return sign

    def get_params(self, data, t):
        token = self.get_token()
        sign = self.get_sign(data, t, token)
        params = {
            "jsv": "2.7.2",
            "appKey": self.app_key,
            "t": t,
            "sign": sign,
            "api": "mtop.1688.imageService.putImage",
            "ignoreLogin": "true",
            "prefix": "h5api",
            "v": "1.0",
            "ecode": "0",
            "dataType": "jsonp",
            "jsonpIncPrefix": "search1688",
            "timeout": "20000",
            "type": "originaljson"
        }
        # print('token:', token, 't:', t, 'appKey:', self.app_key, 'sign:', sign)
        # print('params:', params)
        return params

    def search_image(self, file_or_url_base64):
        """
        以图搜图
        :param file_or_url_base64: 图片地址或文件或base64
        :return:
        """
        datas = []
        t = now_str()
        data = self.get_data(file_or_url_base64)
        params = self.get_params(data, t)
        aip_url = "https://h5api.m.1688.com/h5/mtop.1688.imageservice.putimage/1.0/"
        rs = self.page.post(aip_url, params=params, data=data)

        json_data = rs.json()
        # print(json_data)
        if '调用成功' in rs.text:
            imageId = json_data['data']['imageId']
            list_url = f'https://s.1688.com/youyuan/index.htm?tab=imageSearch&imageAddress=&imageId={imageId}&spm=a260k.dacugeneral.searchbox.input&imageIdList={imageId}'
            self.page.get(list_url)
            self.check_page()
            datas = self.parse_list_by_js()
        else:
            print('调用失败')
            print(rs.text)
            # pg.alert('以图搜图调用失败,请检查')
        return datas


def insert_or_update_1688_listing(datas, params):
    """
    插入或更新1688Listing数据到-数据库中。

    :param datas: 包含单个商品信息的字典。
    :param params: 包含任务及用户相关信息的字典。
    """
    # 从params中提取必要参数
    app_id = params.get('app_id') if params.get('app_id') else ''
    task_id = params.get('task_id') if params.get('task_id') else ''
    date_time = params.get('date_time') if params.get('date_time') else 0
    task_time = params.get('task_time') if params.get('task_time') else 0
    data_status = params.get('data_status') if params.get('data_status') else 1
    user_id = params.get('user_id') if params.get('user_id') else 0
    username = params.get('username') if params.get('username') else ''
    create_time = params.get('create_time') if params.get('create_time') else 0
    platform_num = params.get('platform_num') if params.get('platform_num') else 0
    task_num = len(datas)

    status, result = (10, '成功')  # 默认状态为10，表示成功

    # 定义用于插入或更新的SQL语句，针对`data_1688_listing`表
    sql_upsert_sprite = """
        INSERT INTO `data_1688_listing`(
            `unique_id`, `app_id`, `task_id`, `datetime`, `task_time`, `data_status`, 
            `user_id`, `username`, `asin`, `image`, `factory`, `price`, `price_json`, 
            `sales`, `url`, `create_time`
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            `app_id`=VALUES(`app_id`), `task_id`=VALUES(`task_id`), `datetime`=VALUES(`datetime`),
            `task_time`=VALUES(`task_time`), `data_status`=VALUES(`data_status`), 
            `user_id`=VALUES(`user_id`), `username`=VALUES(`username`), 
            `image`=VALUES(`image`), `factory`=VALUES(`factory`), `price`=VALUES(`price`),
            `price_json`=VALUES(`price_json`), `sales`=VALUES(`sales`), `url`=VALUES(`url`);
    """

    # 准备插入数据的参数列表
    insert_params = [(
        calculate_md5_hash(item.get('url') + str(date_time)), app_id, task_id, date_time, task_time, data_status,
        user_id, username, item.get('asin', ''), item.get('image', ''), item.get('factory', ''), item.get('price', ''),
        item.get('price_json', ''), item.get('sales', ''), item.get('url', ''), create_time
    ) for item in datas]

    # 如果有数据需要插入，则执行插入操作
    if insert_params:
        # 执行插入操作
        rs = MS.insert_many(sql_upsert_sprite, insert_params)
        # 根据插入结果设置状态和结果信息
        status, result = (10, '成功') if rs > 0 else (10, '无新增或需要更新的数据') if rs == 0 else (20, '异常')
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID-{task_id};抓取{task_num}条数据;操作{rs}条{result}')
    else:
        status, result = (10, '无数据')

    if MS.err:
        msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
        logging(msg)
        fsmsg.send('1688数据采集', msg, MS.err)

    # 更新1688任务状态
    MS.update(
            f'update `task_goods` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, 1, now_int(), task_id)
    )


def parse_detail(html):
    if html is None:
        with open('detail.html', 'r', encoding='utf-8') as f:
            html = f.read()
    soup = BeautifulSoup(html, 'html.parser')
    # print(soup.prettify())
    script = soup.select('script')
    datas = {}
    for i in script:
        if i.string is not None:
            if '__GLOBAL_DADA' in i.string:
                code = i.string
                runtime = execjs.get()
                context = runtime.compile(f'var window = {{}};{code}')
                data = context.eval('window.__INIT_DATA')
                tempModel = data['globalData']['tempModel']
                offerBaseInfo = data['globalData']['offerBaseInfo']
                skuRangePrices = data['globalData']['orderParamModel']['orderParam']['skuParam']['skuRangePrices']
                # print(data)
                print(tempModel)
                print(offerBaseInfo)
                print(skuRangePrices)
                title = tempModel["offerTitle"]
                seller_url = f'https://detail.1688.com/offer/{tempModel["offerId"]}.html'
                seller = offerBaseInfo["sellerLoginId"]
                price = skuRangePrices

                print(f'标题：{title}')
                print(f'链接：{seller_url}')
                print(f'供应商：{seller}')
                print(f'价格：{price}')
                datas['title'] = title
                datas['seller_url'] = seller_url
                datas['seller'] = seller
                datas['price'] = price
    return datas


if __name__ == "__main__":
    ali = Ali1688()
    # url = 'https://s.1688.com//youyuan/index.htm/_____tmd_____/punish?x5secdata=xcpHvzjaEJJakjSLjC5cIGzgN3VpyIuk3dvnrc5aelO7cvlbO0x3BsAsgl9pos2roLFVp1umNzmmCLdEzSzLPABRArYVAHvbadvYWLiFQEBihBL2tOBXBBBzVq2ukOZvroqPAfpHJElgWCASNOfM1G8BTwUCYbct1gf1TXe0msNtGTL45N%2fOK4w%2bWFztqgJyG6H%2b4yw4QgXfwziDflZxZvzShWXNJnSJb4Qu0HJFByO3%2flIKfDiq8MRqZzH2WGNgZt3SU9uk1ibVEceINfcAnStN0zogZhS9APW%2f3VuGzOHl2%2b8dbr8jAonHfnLRd6qCUwIa6B4JNxw1VE%2bGCKpqGtRDxbZhO8xcbZyfMzKEDLjss%3d__bx__s.1688.com%2fyouyuan%2findex.htm&x5step=1'  # 普通滑块验证码
    # url = 'https://s.1688.com//youyuan/index.htm/_____tmd_____/punish?x5secdata=xckhd7A0BkfS5vWKP4OhCSqKkpIgkJbkJEgr8xD7vilcS1U%2fU8xKAq9mTgDFvXlLRadJKf09gMuvXgqdSIl7kj6jOhobizbHX3AiQIbSVQFAf4k7yHiYwy4xDOAsbRkQO%2bmhZ%2bPjKBJJ1zNIZadOiytCHG8sTcwan6HDDTVvmPwscIcgj96%2f1BibMN4OurO4uP0JQSw5cJQu1Ug%2f4skgbrrIKSEOExVNmDiC%2fE%2biNbmRUHA%2bqMs2nRxasRSFCch2XkQ1tQsmg%2fAHqy1ZHOtupQspco%2fvA%2fgDYBVXOc22Vm39rVJB1RTV01%2bvPo7EPM3yik6k%2fhoFvFxhZz1q0tyXLsIR6UbCaEoFwqtce%2fQvevF4AW4rBLlBOoTXwCIVdw9whSUE7EZsYPyjYa2SXBhof1yQ%3d%3d__bx__s.1688.com%2fyouyuan%2findex.htm&x5step=1' # 逻辑验证码
    # url = 'https://detail.1688.com/offer/728364201648.html'
    # url = 'https://detail.1688.com/offer/614365437493.html?spm=a26352.b28411319.offerlist.102.6b9b1e62Oxebmb'
    #
    # ali.page.get(url)
    ali.check_page()

    print(ali.page.url)
    print(ali.page)
    print(ali.page.title)
    print(ali.page.html)
