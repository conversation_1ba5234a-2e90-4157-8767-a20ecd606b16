import pymysql
import json
import requests
from utils_mrc.MysqlHelper import *

MS = MysqlHelper()


def fetch_data_from_db():
    """从数据库获取产品信息"""
    sql = '''
    SELECT
        g.id as id,
    	g.goods_sku AS sku,
    	g.goods_name AS product_name,
    	dgs.goods_unit AS unit,
    	g.goods_category_lastid AS category_id,
    	'' AS category,
    	'' as model,
    	g.goods_brand AS brand_id,
    	'' AS brand,
    	g.status AS status,
    	gdl.detail AS description,
    	dgss.stock_user_id AS cg_opt_uid,
    	'' AS cg_opt_username,
    	g.develop_user_id AS product_developer_uid,
    	'' AS product_developer,
    	g.create_user_id AS product_creator_uid,
    	g.admin_job_id AS product_duty_uids,
    	0 as is_append_product_duty,
    	dgss.remarks AS purchase_remark,
    	g.goods_stock AS cg_price,
    	0 as is_related,
    	dgss.delivery_time AS cg_delivery,
    	'' AS cg_product_material,
    	gah.length AS cg_product_length,
    	gah.width AS cg_product_width,
    	gah.height AS cg_product_height,
    	gah.weight_net AS cg_product_net_weight,
    	gah.weight AS cg_product_gross_weight,
    	gah.length AS cg_package_length,
    	gah.width AS cg_package_width,
    	gah.height AS cg_package_height,
    	gah.box_length AS cg_box_length,
    	gah.box_width AS cg_box_width,
    	gah.box_height AS cg_box_height,
    	gah.box_weight_net AS cg_box_weight,
    	gah.box_num AS cg_box_pcs,
    	gde.goods_declare AS bg_customs_export_name,
    	gde.goods_inland_code AS bg_export_hs_code,
    	gde.goods_declare_en AS bg_customs_import_name,
    	'USD' AS currency,
    	gde.goods_declare_usd AS bg_customs_import_price,
    	'- - ' as 其他,
    	g.goods_attribute AS special_attr,-- 假设这是一个可直接获取的属性或需要进一步处理的字段
    	gde.goods_declare_company AS customs_declaration_unit,
    	dgss.factory AS erp_supplier_id,
    	dgss.factory AS supplier_id,
    	dgss.url AS supplier_product_url,
    	dgss.remarks AS quote_remark,
    	1 AS is_primary,
    	'USD' as quotes_currency,
    	dgss.tax AS tax,
    	dgss.price as price
    FROM
    	gms.goods g
    	LEFT JOIN gms.dev_goods_sku dgs ON g.id = dgs.goods_id
    	LEFT JOIN gms.goods_detail gdl ON g.id = gdl.goods_id 
    	AND gdl.lang = 'cn'
    	LEFT JOIN gms.dev_goods_sku_stock dgss ON g.id = dgss.goods_id
    	LEFT JOIN gms.goods_attach gah ON g.id = gah.goods_id
    	LEFT JOIN gms.goods_declare gde ON g.id = gde.goods_id
    '''
    result = MS.get_dict(sql)
    return result


def get_goods_picture_list(id):
    sql = f"SELECT a.url AS pic_url, CASE type WHEN 0 THEN 1 ELSE 0  END AS is_primary  FROM gms.`goods_image` a  WHERE EXISTS (SELECT 1  FROM gms.goods b  WHERE b.id = a.goods_id  AND b.id = {id} )"
    result = MS.get_dict(sql)
    return result


def get_goods_group_list(id):
    sql = f"SELECT a.goods_sku AS sku, a.bom_goods_num AS quantity  FROM gms.`goods_bom` a  WHERE EXISTS (SELECT 1  FROM gms.goods b  WHERE b.id = a.goods_id  AND b.id = {id} )"
    result = MS.get_dict(sql)
    return result


def process_data(db_results):
    """数据预处理，如有需要"""
    # 这里可以根据需要添加数据处理逻辑
    # 示例：确保某些字段存在，即使为空
    data_list = []
    for row in db_results:
        requests_json = {
            "sku": row['sku'],
            "product_name": row['product_name'],
            "picture_list": get_goods_picture_list(row['id']),
            "unit": row['unit'] if row['unit'] else '',
            "category_id": row['category_id'],
            "category": row['category'],
            "model": row['model'],
            "brand_id": row['brand_id'],
            "brand": row['brand'],
            "status": row['status'],
            "description": row['description'],
            "group_list": get_goods_group_list(row['id']),
            "cg_opt_uid": row['cg_opt_uid'],
            "cg_opt_username": row['cg_opt_username'],
            "product_developer_uid": row['product_developer_uid'],
            "product_developer": row['product_developer'],
            "purchase_remark": row['purchase_remark'],
            "cg_price": row['cg_price'],
            "is_related": row['is_related'],
            "cg_delivery": row['cg_delivery'] if row['cg_delivery'] else 0,
            "cg_product_material": row['cg_product_material'],
            "cg_product_length": row['cg_product_length'],
            "cg_product_width": row['cg_product_width'],
            "cg_product_height": row['cg_product_height'],
            "cg_product_net_weight": row['cg_product_net_weight'],
            "cg_product_gross_weight": row['cg_product_net_weight'],
            "cg_package_length": row['cg_package_length'],
            "cg_package_width": row['cg_package_width'],
            "cg_package_height": row['cg_package_height'],
            "cg_box_length": row['cg_box_length'],
            "cg_box_width": row['cg_box_width'],
            "cg_box_height": row['cg_box_height'],
            "cg_box_weight": row['cg_box_weight'],
            "cg_box_pcs": row['cg_box_pcs'],
            "bg_customs_export_name": row['bg_customs_export_name'],
            "bg_export_hs_code": row['bg_export_hs_code'],
            "bg_customs_import_name": row['bg_customs_import_name'],
            "currency": row['currency'],
            "bg_customs_import_price": row['bg_customs_import_price'],
            "product_creator_uid": row['product_creator_uid'],
            "product_duty_uids": [row['product_duty_uids']],
            "is_append_product_duty": row['is_append_product_duty'],
            "qc_standard": {
                "custom_qc_template": {
                    "qc_image": [{
                        "file_id": "",
                        "customer_url": ""
                    }][:10]
                }
            },
            "product_logistics_list": {
                "US_cg_transport_costs": 0,
                "US_currency": "",
                "US_clearance_price": 0,
                "US_clearance_price_currency": "",
                "US_bg_import_hs_code": "",
                "US_bg_tax_rate": 0
            },
            "supplier_quote": [{
                "erp_supplier_id": row['erp_supplier_id'],
                "supplier_id": row['supplier_id'],
                "supplier_product_url": [row['supplier_product_url']][:20],
                "quote_remark": row['quote_remark'],
                "is_primary": row['is_primary'],
                "quotes": [{
                    "currency": row['quotes_currency'],
                    "is_tax": 1 if row['tax'] else 0,
                    "tax_rate": row['tax'],
                    "step_prices": [{
                        "moq": 1,
                        "price_with_tax": row['price'] * (1 + row['tax']) if row['tax'] and row['price'] else 0
                    }]
                }]
            }],
            "special_attr": [row['special_attr']],
            "declaration": {
                "customs_declaration_unit": row['customs_declaration_unit'],
                "customs_declaration_spec": "",
                "customs_declaration_origin_produce": "",
                "customs_declaration_inlands_source": "",
                "customs_declaration_exempt": ""
            },
            "clearance": {
                "customs_clearance_material": "",
                "customs_clearance_usage": "",
                "customs_clearance_internal_code": "",
                "customs_clearance_preferential": 0,
                "customs_clearance_brand_type": 0,
                "customs_clearance_product_pattern": "",
                "allocation_remark": "",
                "weaving_mode": 0,
                "customs_clearance_pic_url": ""
            }
        }
        data_list.append(requests_json)
    return data_list


def main():
    result = fetch_data_from_db()
    processed_products = process_data(result)
    print(processed_products)


if __name__ == "__main__":
    main()
