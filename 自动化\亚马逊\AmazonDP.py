# -*- coding:UTF-8 -*-
# @FileName  :AmazonDP.py
# @Time      :2024/6/5 14:28
# <AUTHOR>
from utils_mrc.pub_fun import *
import random
import socket
import copy
import json
import os
import re
import time
import traceback
import amazoncaptcha
import requests
from datetime import datetime, date
from DrissionPage import ChromiumPage, ChromiumOptions, WebPage
from urllib.parse import urlparse, quote
from utils_mrc.MysqlHelper import *
from utils_mrc.FeiShuAPI import *
from TimePinner import Pinner
from utils_mrc.AccountManager import *
from tqdm import tqdm
from work.自动化.卖家精灵.SellerSpriteExpansion import sse
from work.自动化.西柚.XiYouZhaoCi import xyzc
from DrissionPage.errors import *
from utils_mrc.ProxyExtensionGenerator import ProxyExtensionGenerator
from utils_mrc.SpiderTools import IPPool
from functools import wraps


def retry_on_exception(retry_count=3, msg='数据解析异常'):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            attempts = 0
            while True:
                try:
                    self.change_zipcode()
                    # 尝试第一次解析数据
                    return func(self, *args, **kwargs)
                except ContextLostError:
                    # 处理 ContextLostError
                    self.page = WebPage(chromium_options=self.co)
                    continue
                except Exception as e:
                    # 处理解析发生的异常
                    if self.page.mode == 'd':
                        check_result = self.check_page()
                    else:
                        check_result = self.check_s_page()
                    if not check_result:
                        return []  # 页面检查失败，返回空列表
                    # 如果页面检查通过，进行重试
                    attempts += 1  # 增加重试次数
                    if attempts > retry_count:
                        # 如果不是第一次尝试，记录异常信息
                        traceback.print_exc()
                        self.cur_info['finally_result'] += f' |[第{self.cur_info.get("cur_page")}页]{msg}：{e}'
                        self.cur_info['finally_status'] = 20
                        return []  # 超过最大重试次数，返回空列表

        return wrapper

    return decorator


class AmazonDP:
    def __init__(self, local_port=None, proxy_type=1):
        self.local_port = local_port or get_chrome_free_port() or 9222
        self.proxy_type = int(proxy_type)
        co = ChromiumOptions()
        co.remove_extensions()
        clear_plugin_cache_port(self.local_port)
        # co.add_extension(r'D:\Documents\webProject\Proxy_SwitchyOmega') if self.proxy_type else ''
        if self.proxy_type: co.add_extension(ProxyExtensionGenerator.generate_proxy_extension(proxy_type))
        self.proxies = ''  # 默认使用本地IP
        self.max_total = 10000
        self.err_count = 0  # 连续出错次数
        self.task_params = {}
        self.set_proxy = False  # 是否已经设置了代理
        self.download_path = r'D:\rpa_download\amazon'
        co.set_argument("--disable-gpu")  # 禁用GPU加速可以避免浏览器在加载页面时使用过多的计算资源，从而提高加载速度
        co.set_argument('--no-default-store_info-check')
        co.set_argument('--disable-suggestions-ui')
        co.set_argument('--disable-popup-blocking')
        co.set_argument('--hide-crash-restore-bubble')
        co.set_argument('--disable-features=PrivacySandboxSettings4')
        co.set_local_port(self.local_port)
        # co.no_imgs()  # 不加载图片
        self.co = co
        # self.page = ChromiumPage(addr_or_opts=self.co)
        self.page = WebPage(chromium_options=self.co)
        self.page.set.auto_handle_alert(accept=True)
        # self.page.set.blocked_urls('*.css*')  # 不加载css文件
        self.page.set.window.mini()
        if is_test:
            self.page.set.window.show()
            self.page.set.window.max()
        self.page.set.timeouts(120)
        self.output_folder = 'amazon_screenshots'  # 输出截图的文件夹路径
        self.asin_locator = '@@data-asin^B@@data-index@@data-component-type=s-search-result'
        self.cur_info = init_fetch_info()
        self.proxy_retry = 3
        if '.amazon.' not in self.page.url: self.page.get('https://www.amazon.de/')
        self.page.cookies_to_session()

    def switch_ip(self, ip_port=None):
        err = ''
        tab = self.page.new_tab()
        for _ in range(5):
            try:
                if ip_port:
                    logging(f"~~~正在切换ip:{ip_port}")
                    load_status = tab.get("chrome-extension://padekgcemlokbadohgkifijomclgjgif/options.html#!/profile/proxy")
                    if not load_status or tab.url == 'about:blank': raise Exception('加载代理插件异常')

                    tab.ele('c=[ng-model="proxyEditors[scheme].scheme"]').select('HTTP')
                    if self.proxy_type == 2:
                        ip = '*************'
                        port = '26868'
                        p_user = 'czc01'
                        p_pwd = 'WEsadby@1asddasdom@12234232423'
                    else:
                        ip, port = ip_port.split(":")
                        p_user = '366B3BA3'
                        p_pwd = 'B163308B370C'
                    tab.ele('x://input[@ng-model="proxyEditors[scheme].host"]').input(ip, clear=True)
                    tab.ele('x://input[@ng-model="proxyEditors[scheme].port"]').input(port, clear=True)
                    tab.wait(0.5)
                    btn_login = tab.ele('c=[title="代理登录"]')
                    btn_login.click()
                    inp_user = tab.ele('c=.input-group [placeholder="用户名"]')
                    inp_user.input(p_user, clear=True)
                    tab.ele('c=.input-group [name="password"]').input(p_pwd, clear=True)

                    tab.ele('c=[class="btn btn-primary ng-binding"]').click()
                    tab.wait(1)
                    tab.ele('x://a[@ng-click="applyOptions()"]').click()
                    # tab.wait(2)
                    tab.get("chrome-extension://padekgcemlokbadohgkifijomclgjgif/popup/index.html#")
                    # tab.wait(2)

                    for _1 in range(3):
                        if tab.wait.eles_loaded('x://span[text()="proxy"]', timeout=2, any_one=True, raise_err=False):
                            break
                        tab.refresh()
                        tab.wait.load_start(raise_err=False)
                    tab.ele('x://span[text()="proxy"]').click()
                    self.set_proxy = True
                    self.proxies = ip_port
                else:
                    tab = self.page.new_tab()
                    tab.get("chrome-extension://padekgcemlokbadohgkifijomclgjgif/popup/index.html#")
                    tab.ele('x://span[text()="[直接连接]"]').click()
                    self.set_proxy = False
                time.sleep(1)
                if len(self.page.tab_ids) > 1:
                    tab.close()
                break
            except PageDisconnectedError:
                self.page = WebPage(chromium_options=self.co)
                tab = self.page.new_tab()
            except:
                err = traceback.format_exc()
                tab.refresh()
                tab.wait.load_start()
        else:
            print(f"切换ip失败，错误信息：{err}")
        logging('ip切换执行完毕')

    def parse_search_image_data(self, img_data, site: None):
        if site is None:
            site = self.get_site_by_url()
        self.page.change_mode('s')
        href = '/'.join(url.split('/', )[:3])
        search_url = f'{href}/stylesnap?q=local'
        self.page.get(search_url)
        token = self.page('@name=stylesnap').attr('value')
        params = {
            "stylesnapToken": token
        }
        files = {'explore-looks.jpg': ('explore-looks.jpg', img_data, 'image/jpeg')}
        # self.page.post(f"https://www.amazon.{site}/stylesnap/upload", params=params, files=files)
        self.page.post(f"{href}/stylesnap/upload", params=params, files=files)
        result = self.page.json or {}
        searchResults = result.get('searchResults')
        if not searchResults:
            logging(f'搜索失败！')
            print(result)
            return []
        return searchResults[0].get('bbxAsinMetadataList')

    def fetch_search_image_price(self, content) -> list:
        self.cur_info = init_fetch_info()
        self.page.change_mode('s')
        self.page.get(content)

        @retry_on_exception()
        def get_data(self):
            img = self.page('c=[data-csa-c-action="image-block-main-image-hover"] img', timeout=2)
            # img_data = img.src()
            img = self.page('c=[data-old-hires]', timeout=2)
            src = img.attr('src')
            img_data = self.page.session.get(src).content
            return img_data

        site = self.get_site_by_url()
        results = self.parse_search_image_data(get_data(self), site)

        def update(x: dict):
            x['site'] = site
            x['currency'], x['price'] = separate_currency_price(x['price'])
            try:
                del x['colorSwatches']
                del x['twisterVariations']
            except: pass
            return x

        results = [update(x) for x in results]
        return results

    def get_site_by_url(self, url=None):
        if not url:
            url = self.page.url
        if not url:
            logging('获取当前页面地址为空，请确认已访问站点地址,已默认为德国站点')
            return 'de'
        domain = urlparse(url).netloc
        site = domain.split('.')[-1]
        site = AmazonConfig.get_local_site(site)
        return site

    def get_asins_by_url_pages(self, url_param, pages_param=1):
        # 每次抓取前，还原本次任务基本抓取状态
        self.cur_info = init_fetch_info()
        if pages_param <= 0:  # 默认抓取前20页
            pages_param = 999
        t1 = time.time()
        logging(f'开始计时,当前时间: {datetime.today()}')
        self.cur_info['cur_page'] = 1
        datas = []
        try:
            with tqdm(total=pages_param, desc=f'数据采集 {url_param}', unit='页', file=sys.stdout) as pbar:
                while True:
                    if self.cur_info['cur_page'] > pages_param or self.cur_info['total'] >= self.max_total or '未返回结果' in self.cur_info['result']:
                        # print(f'超过抓取上限，终止')
                        pbar.close()
                        break
                    t2 = time.time()
                    if pages_param != 1:  # 如果不指定页数，抓取地址页
                        url_param = update_url_param(url_param, 'page', self.cur_info['cur_page'])
                    self.cur_info['url'] = url_param
                    pbar.set_description(f'正在采集 {url_param} 第 {self.cur_info["cur_page"]} 页')  # 更新进度条描述
                    self.page.get(url_param, timeout=60, retry=self.proxy_retry)
                    self.cur_info['real_page'] = self.cur_info["cur_page"]

                    datas_ = self.parse_of_page_list()
                    datas.extend(datas_)
                    self.cur_info['total'] = len(datas)

                    # 更新进度条后缀
                    pbar.set_postfix({
                        f'第{self.cur_info["cur_page"]}页数量': len(datas_),
                        '当前总数量': len(datas),
                        '当前asin总数量': len(datas),
                        '用时': f'{time.time() - t2:.2f}s',
                        '总耗时': f'{time.time() - t1:.2f}s'
                    })

                    pbar.update(1)  # 更新进度条
                    self.cur_info['cur_page'] += 1
            self.err_count = 0
        except Exception as e:
            err_info = traceback.format_exc()
            self.err_count += 1
            self.cur_info['finally_result'] += f' |抓取异常:[第{self.cur_info["cur_page"]}页] {e}'
            self.cur_info['finally_status'] = 20
            print(err_info)
            fsmsg.send('亚马逊数据采集', self.cur_info['finally_result'], err_info)
            self.page = WebPage(chromium_options=self.co)
            self.page.reconnect()
            if self.err_count >= 20:
                msg = '连续触发异常超20次，程序已暂停，请先处理各种异常信息！'
                fsmsg.send('亚马逊数据采集', msg, err_info)
                pg.alert(msg)

        print(f'爬取结束,总页数{self.cur_info["real_page"]},总asin数量: {len(datas)},总用时:{time.time() - t1}')
        if self.cur_info.get('total', 0) >= self.max_total:
            self.cur_info['result'] = '成功'
            self.cur_info['status'] = 10
        else:
            self.cur_info['result'] = '成功' if self.cur_info['finally_status'] == 10 else self.cur_info['finally_result']
            self.cur_info['status'] = self.cur_info['finally_status']

        self.cur_info['result'] += f'| 实抓页数:{self.cur_info["real_page"]}'
        return datas

    def get_asins_loc_index(self, url_param, pages_param=3):
        # 每次抓取前，还原本次任务基本抓取状态
        self.cur_info = init_fetch_info()
        t1 = time.time()
        logging(f'开始计时,当前时间: {datetime.today()}')
        self.cur_info['cur_page'] = 1
        datas = []
        url_keyword = url_get_keyword(url_param)
        try:
            if not url_keyword:
                self.cur_info['finally_status'] = 99
                raise Exception("搜索关键词不能为空")
            with tqdm(total=pages_param, desc=f'数据采集 {url_param}', unit='页', file=sys.stdout) as pbar:
                while True:
                    t2 = time.time()
                    if pages_param != 1:  # 如果不指定页数，抓取地址页
                        url_param = update_url_param(url_param, 'page', self.cur_info['cur_page'])
                    self.cur_info['url'] = url_param
                    pbar.set_description(f'正在采集 {url_param} 第 {self.cur_info["cur_page"]} 页')  # 更新进度条描述
                    self.page.get(url_param, timeout=60, retry=self.proxy_retry)
                    self.cur_info['real_page'] = self.cur_info["cur_page"]

                    @retry_on_exception(msg='广告位解析异常')
                    def get_data(self):
                        datas_ = []
                        ele_asins = self.page.s_eles(self.asin_locator)
                        if not ele_asins: raise Exception('未找到ASIN元素')
                        site = self.get_site_by_url()
                        for loc_index, div in enumerate(ele_asins, start=1):
                            # div = div.s_ele()
                            cur_dict = {}
                            asin = div.attr('data-asin')
                            self.cur_info['all_loc_index'] += 1
                            is_ad = 1 if 'aok-inline-block puis-sponsored-label-info-icon' in div.inner_html else 0  # 广告
                            ele_price = div('@data-cy=price-recipe', timeout=2)
                            if 'reviews-block' in div.inner_html:
                                if 'a-color-secondary' in div('@data-cy=reviews-block').inner_html:
                                    ztxl = div('@data-cy=reviews-block')('c:.a-color-secondary')
                                    ztxl_text = ztxl.text if ztxl else '0'
                                    child_sales = extract_number_sales(ztxl_text)
                                    cur_dict['child_sales'] = child_sales
                                if ele_price and 'sx-red-mvt' in ele_price.inner_html:
                                    tag_spike = 1
                                    cur_dict['tag_spike'] = tag_spike
                            if ele_price and 's-coupon-component' in ele_price.inner_html:
                                yh = ele_price('c:.s-coupon-unclipped>span')
                                coupon = yh.text if yh else ''
                                cur_dict['coupon'] = coupon

                            e_title = div.ele('t:h2')
                            e_price = div.ele('c=[class="a-offscreen"]')
                            e_image = div.ele('c=img.s-image')
                            if not e_price or not e_title or not e_image:
                                continue
                            title = e_title.text
                            symbol, price = separate_currency_price(e_price.text)
                            img = div.ele('c=img.s-image').attr('src')

                            e_score = div.ele('.a-icon-alt')
                            stars = e_score.text.split()[0].replace(',', '.') if e_score else 0

                            cur_dict.update({
                                'asin': asin,
                                'page_index': self.cur_info["cur_page"],
                                'loc_index': loc_index,
                                'all_loc_index': self.cur_info['all_loc_index'],
                                'is_ad': is_ad,
                                'url': self.cur_info['url'],
                                'site': site,
                                'page_count': len(ele_asins),
                                'title': title,
                                'price': price,
                                'img': img,
                                'keywords': url_keyword,
                                'stars': stars,
                            })
                            datas_.append(cur_dict)
                        return datas_

                    datas_ = get_data(self)
                    datas.extend(datas_)
                    self.cur_info['total'] = len(datas)

                    # 更新进度条后缀
                    pbar.set_postfix({
                        f'第{self.cur_info["cur_page"]}页数量': len(datas_),
                        '当前总数量': len(datas),
                        '当前asin总数量': len(datas),
                        '用时': f'{time.time() - t2:.2f}s',
                        '总耗时': f'{time.time() - t1:.2f}s'
                    })
                    pbar.update(1)  # 更新进度条
                    self.cur_info['cur_page'] += 1
                    if self.cur_info['cur_page'] > pages_param or self.cur_info['total'] >= self.max_total:
                        break

            self.err_count = 0
        except Exception as e:
            err_info = traceback.format_exc()
            self.err_count += 1
            self.cur_info['finally_result'] += f' |抓取异常:[第{self.cur_info["cur_page"]}页] {e}'
            self.cur_info['finally_status'] = max(self.cur_info['finally_status'], 20)
            print(err_info)
            fsmsg.send('亚马逊数据采集', self.cur_info['finally_result'], err_info)
            self.page = WebPage(chromium_options=self.co)
            self.page.reconnect()
            if self.err_count >= 20:
                msg = '连续触发异常超20次，程序已暂停，请先处理各种异常信息！'
                fsmsg.send('亚马逊数据采集', msg, err_info)
                pg.alert(msg)

        print(f'爬取结束,总页数{self.cur_info["real_page"]},总asin数量: {len(datas)},总用时:{time.time() - t1}')
        self.cur_info['result'] = '成功' if self.cur_info['finally_status'] == 10 else self.cur_info['finally_result']
        self.cur_info['status'] = max(self.cur_info['finally_status'], self.cur_info['status'])

        self.cur_info['result'] += f'| 实抓页数:{self.cur_info["real_page"]}'
        return datas

    def wait_page_loaded(self, timeout=120, raise_err=False):
        is_page_loaded = False
        error_message = ''

        while timeout > 0:
            try:
                is_page_loaded = self.page.run_js_loaded('return document.readyState === "complete"')
                if is_page_loaded:
                    # logging('页面加载完成！')
                    break
                else:
                    time.sleep(1)
            except PageDisconnectedError:
                self.page = WebPage(chromium_options=self.co)
                time.sleep(1)
            except Exception as e:
                error_message = str(e)
                # logging(f'页面加载异常: {error_message}')
                time.sleep(1)
            finally:
                timeout -= 1
                # logging(f'剩余时间: {timeout}')

        if not is_page_loaded:
            if raise_err:
                raise Exception(f'页面加载超时，错误信息: {error_message}')
            else:
                # logging(f'页面加载超时，但不抛出异常，错误信息: {error_message}')
                pass

    def check_page(self):
        try:
            pin.pin('等待页面加载开始记录')
            self.wait_page_loaded()

            pin.pin('代理检查')
            for _ in range(3):
                if 'chrome-error' in self.page.run_js_loaded('return location.href') or not self.page.wait.doc_loaded(timeout=120, raise_err=False):
                    logging(f'ip异常，正在刷新页面...')
                    time.sleep(2)
                    self.page.refresh()
                    self.page.wait.load_start()
                else:
                    break
            else:
                self.cur_info['finally_result'] = f' |[第{self.cur_info["cur_page"]}页] 无法访问链接，请检查访问链接或代理IP'
                self.cur_info['finally_status'] = 20
                logging(self.cur_info['finally_result'])
                # return False

            pin.pin('检查页面开始记录')
            if 'ref=cs_404_link' in self.page.html:
                # self.cur_info['result'] = f' |[第{self.cur_info["cur_page"]}页] 404页面'
                self.cur_info['cur_asin_result'] = f' |404页面'
                self.cur_info['status'] = 10
                return False
            for i1 in range(3):  # 验证码最大重试3次
                cur_url, site = self.get_url_info()
                self.cur_info['site'] = site
                pin.pin('等待页面加载')

                if 'id="captchacharacters"' in self.page.html:  # 验证码
                    # https://www.amazon.com/errors/validateCaptcha
                    self.cur_info['result'] = '检测到验证码页面...'
                    self.cur_info['status'] = 20
                    print(self.cur_info['result'], end=' ')
                    img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
                    # print(img_url)
                    print(f'验证码第{i1 + 1}次识别中...', end=' ')
                    captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
                    print('本次识别结果为：', captcha_result)
                    self.page('#captchacharacters').input(captcha_result + '\n')
                    continue
                pin.pin('验证码')

                count_503 = 0
                while 'ref=cs_503_link' in self.page.html:
                    count_503 += 1
                    tqdm.write(f'\r503页面{count_503}次', end=' ')
                    e_503 = self.page('@href:ref=cs_503_link', timeout=2)
                    if e_503:
                        e_503.click()
                        time.sleep(1)
                    self.page.refresh()
                    self.page.wait.load_start(raise_err=False)
                    self.page.get(cur_url)
                    self.wait_page_loaded()
                pin.pin('503')

                # if 'sp-cc-accept' in self.page.html:
                #     self.page('#sp-cc-accept', timeout=3).click()

                self.change_zipcode()  # 检查并切换地址

                pin.pin('检查登录记录')
                if '/s?' in self.page.url:  # 搜索页面的操作
                    if 'class="a-size-medium a-color-base"' in self.page.html:
                        self.cur_info['result'] = f' |[第{self.cur_info["cur_page"]}页] 搜索页面未返回结果'
                        self.cur_info['status'] = 10
                        logging(self.cur_info['result'])
                        return False
                    pin.pin('是否有结果')
                    for rt in range(3):
                        self.wait_page_loaded()
                        if 'data-asin' in self.page.html and not self.page('@data-asin^B'):  # asin数据
                            self.cur_info['result'] = '当前搜索页未检测到asin数据'
                            self.cur_info['status'] = 20
                            print(self.cur_info['result'])
                            self.page.refresh()
                            self.page.wait.load_start(raise_err=False)
                            continue
                        pin.pin(f'{rt}检查asin')
                        break
                    else:
                        self.cur_info['result'] = '搜索页异常重试失败超过3次'
                        self.cur_info['status'] = 20
                        print(self.cur_info['result'])
                        return False

                if '/dp/' in self.page.url:
                    for _ in range(3):
                        if self.page('#productTitle', timeout=3):
                            break
                        self.page.refresh()
                        self.page.wait.load_start(raise_err=False)
                    else:
                        self.cur_info['result'] = '|详情页超过最大重试次数未检测到标题'
                        self.cur_info['finally_result'] += self.cur_info['result']
                        self.cur_info['finally_status'] = 20
                pin.pin(f'检查搜索页完成')
                return True  # 已检测到可能的异常全处理完则跳出页面页面检测
            else:
                self.cur_info['result'] = '|页面检测未通过！'
                self.cur_info['finally_result'] += self.cur_info['result']
                self.cur_info['status'] = 20
                print(self.cur_info['result'])
                raise Exception(self.cur_info['result'])
        except PageDisconnectedError:
            self.page = WebPage(chromium_options=self.co)
            return self.check_page()
        except ContextLostError:
            self.page = WebPage(chromium_options=self.co)
            self.page.refresh()
            self.page.wait.load_start(raise_err=False)
            return self.check_page()

    def check_s_page(self):
        cur_url = ''
        try:
            cur_url, site = self.get_url_info()
            domain = urlparse(cur_url).netloc
            if 'ref=cs_404_link' in self.page.html:
                # self.cur_info['result'] = f' |[第{self.cur_info["cur_page"]}页] 404页面'
                self.cur_info['cur_asin_result'] = f' |404页面'
                self.cur_info['status'] = 10
                return False
            for i1 in range(5):  # 验证码最大重试3次
                self.cur_info['site'] = site

                if 'id="captchacharacters"' in self.page.html:  # 验证码
                    # https://www.amazon.com/errors/validateCaptcha
                    self.cur_info['result'] = '检测到验证码页面...'
                    self.cur_info['status'] = 20
                    print(self.cur_info['result'], end=' ')
                    img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
                    # print(img_url)
                    print(f'验证码第{i1 + 1}次识别中...', end=' ')
                    captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
                    print('本次识别结果为：', captcha_result)

                    amzn = self.page('c=input[name="amzn"]').attr('value')
                    amznr = self.page('c=input[name="amzn-r"]').attr('value')
                    # print(amzn, amznr)
                    params = {
                        "amzn": amzn,
                        "amzn-r": amznr,
                        "field-keywords": captcha_result
                    }
                    self.page.get(f"https://{domain}/errors/validateCaptcha", params=params)
                    self.page.get(cur_url)
                    continue

                count_503 = 0
                while 'ref=cs_503_link' in self.page.html:
                    count_503 += 1
                    tqdm.write(f'\r503页面{count_503}次', end=' ')
                    self.page.get(cur_url)

                self.change_zipcode()  # 检查并切换地址

                if '/s?' in self.page.url:  # 搜索页面的操作
                    if 'class="a-size-medium a-color-base"' in self.page.html:
                        self.cur_info['result'] = f' |[第{self.cur_info["cur_page"]}页] 搜索页面未返回结果'
                        self.cur_info['status'] = 10
                        logging(self.cur_info['result'])
                        return False
                    if 'data-asin' in self.page.html and not self.page('@data-asin^B'):  # asin数据
                        self.cur_info['result'] = '当前搜索页未检测到asin数据'
                        self.cur_info['status'] = 20
                        print(self.cur_info['result'])
                        self.page.get(cur_url)
                        continue
                if '/dp/' in self.page.url and self.page('#productTitle', timeout=3):
                    self.cur_info['result'] = '|详情页超过最大重试次数未检测到标题'
                    self.cur_info['finally_result'] += self.cur_info['result']
                    self.cur_info['finally_status'] = 20
                    self.page.get(cur_url)
                return True  # 已检测到可能的异常全处理完则跳出页面页面检测
            else:
                self.cur_info['result'] = '|页面检测未通过！'
                self.cur_info['finally_result'] += self.cur_info['result']
                self.cur_info['status'] = 20
                print(self.cur_info['result'])
                raise Exception(self.cur_info['result'])
        except (PageDisconnectedError, ContextLostError):
            self.page = WebPage(chromium_options=self.co)
            self.page.get(cur_url)
            return self.check_page()

    def change_zipcode(self):
        try:
            if '.amazon.' in self.page.url:
                ingress = self.page('#glow-ingress-block', timeout=1)
                if ingress:
                    element_text = ingress.text
                    if any(item in element_text for item in AmazonConfig.ADDRESS_CODE):
                        return
                    self.page = WebPage(chromium_options=self.co)
                    cur_mode = self.page.mode
                    self.page.change_mode('d')
                    js_code = """
                         var zipcodedict = {
                        'co.uk': ['W1S 3PR', 'W1S 3'],
                        'es': '28035',
                        'it': '00185',
                        'fr': '75020',
                        'de': '14199',
                        'com': ['90001', '10001'] // 以数组形式存储多个匹配值
                    };
            
                    var domain = window.location.hostname; // 获取当前网页的域名
                    let zipcodeText = document.querySelector('#nav-global-location-popover-link').innerText;
                    console.log('zipcodeText:', zipcodeText);
                    console.log('domain:', domain);
            
                    let matchFound = false;
                    var zipcode = '90001';
            
                    // 遍历字典的键
                    var domain_code = domain.split("amazon.")[1]
                    const value = zipcodedict[domain_code];
            
                    // 判断是否为数组
                    if (Array.isArray(value)) {
                        // 如果值是数组，判断元素值是否包含在数组中的任何一个值
                        if (!value.some(item => zipcodeText.includes(item))) {
                            matchFound = true;
                            zipcode = value[0];
                        }
                    } else {
                        // 如果值不是数组，直接比较元素值和键的值
                        if (zipcodeText.indexOf(value) == -1) {
                            zipcode = value;
                            matchFound = true;
                        }
                    }
            
                    console.log('zipcode:', zipcode, 'matchFound', matchFound);
            
                    // 包含10001或者90001
                    if (matchFound) {
                        const get_data = document.querySelector("#nav-global-location-data-modal-action").getAttribute('data-a-modal');
                        console.log(get_data);
                        console.log(JSON.parse(get_data)["url"], JSON.parse(get_data)['ajaxHeaders']["anti-csrftoken-a2z"])
                        async function fetchData() {
                            const response = await fetch("https://" + domain + JSON.parse(get_data)["url"], {
                                headers: {
                                    "accept": "text/html,*/*",
                                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                                    "anti-csrftoken-a2z": JSON.parse(get_data)['ajaxHeaders']["anti-csrftoken-a2z"],
                                    "device-memory": "8",
                                    "downlink": "10",
                                    "dpr": "1",
                                    "ect": "4g",
                                    "rtt": "200",
                                    "sec-ch-device-memory": "8",
                                    "sec-ch-dpr": "1",
                                    "sec-ch-ua": '"Chromium";v="116", "Not)A;Brand";v="24", "Microsoft Edge";v="116"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-ch-viewport-width": "881",
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "viewport-width": "881",
                                    "x-requested-with": "XMLHttpRequest"
                                },
                                referrer: location.href,
                                referrerPolicy: "strict-origin-when-cross-origin",
                                credentials: "include"
                            });
            
                            const html = await response.text();
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, "text/html");
                            const scriptElement = doc.querySelector("div script");
            
                            if (scriptElement) {
                                const scriptContent = scriptElement.textContent;
                                console.log(scriptContent)
            
                                // 判断标签内容是否包含 CSRF_TOKEN
                                if (scriptContent.indexOf('CSRF_TOKEN') !== -1) {
                                    // 提取 CSRF_TOKEN 的值
                                    console.log("匹配到安全sign")
                                    var regex1 = /CSRF_TOKEN\s*:\s*"([^"]+)"/;
                                    var match = scriptContent.match(regex1);
                                    var csrfToken = match[1];
                                    console.log('Extracted CSRF_TOKEN:', csrfToken);
                                    const url = JSON.parse(get_data)["url"];
                                    const regex = /pageType=([^&]+)/;
                                    const match_url = url.match(regex);
            
                                    const pageType = match_url[1];
            
                                    console.log("pageType =", pageType);
                                    var pageurl = location.href;
                                    var storecontext = "generic"
                                    if (pageurl.indexOf("/dp") != -1) {
                                        storecontext = "wireless"
                                    }
                                    change_zipcode(zipcode, storecontext, pageType, csrfToken)
            
                                    // 在这里可以根据需要处理 CSRF_TOKEN 的值
                                }
                            }
                        }
                        fetchData();
            
                    }
                     async function change_zipcode(zipcode, storecontext, pageType, csrftoken) {
                        const url = "https://" + domain + "/portal-migration/hz/glow/address-change?actionSource=glow";
            
                        const data = {
                            "locationType": "LOCATION_INPUT",
                            "zipCode": zipcode,
                            "storeContext": storecontext,
                            "deviceType": "web",
                            "pageType": pageType,
                            "actionSource": "glow"
                        };
            
                        const response = await fetch(url, {
                            method: "POST",
                            headers: {
                                "accept": "text/html,*/*",
                                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                                "anti-csrftoken-a2z": csrftoken,
                                "content-type": "application/json",
                                "sec-ch-ua": '"Chromium";v="116", "Not)A;Brand";v="24", "Microsoft Edge";v="116"',
                                "sec-ch-ua-mobile": "?0",
                                "sec-ch-ua-platform": '"Windows"',
                                "sec-ch-ua-platform-version": "10.0.0",
                                "sec-fetch-dest": "empty",
                                "sec-fetch-mode": "cors",
                                "sec-fetch-site": "same-origin",
                                "x-requested-with": "XMLHttpRequest"
                            },
                            referrerPolicy: "strict-origin-when-cross-origin",
                            body: JSON.stringify(data),
                            credentials: "include"
                        });
            
                        const result = await response.json();
                        console.log(result); // 可根据需要处理返回结果
                        location.reload(); //刷新页面
                    }
                        """
                    self.page.run_js_loaded(js_code)
                    self.wait_page_loaded()
                    self.page.change_mode(cur_mode)
        except: pass

    @retry_on_exception()
    def parse_of_page_list(self):
        """
        解析页面列表中的数据。

        该方法从页面中提取特定元素的信息，包括商品的ASIN、标题、评分、评论数、价格、图片URL、配送方式和卖家数量。
        它处理每个页面直到所有相关数据被提取完毕。

        Returns:
            list: 包含所有提取到的数据的列表，每个元素是一个字典，代表一个商品的详细信息。
        """
        # 初始化一个空列表，用于存储解析的数据
        datas = []
        # 定义定位器，用于查找特定元素 # [data-asin^="B"][data-uuid][data-index][data-component-type="s-search-result"]

        site = self.get_site_by_url()
        for _wait_asin in range(5):
            self.page.wait.eles_loaded(self.asin_locator, raise_err=True)  # 等待页面元素加载完成
            # 插件数据加载后，再重新获取页面上的所有匹配定位器的元素
            s_ele = self.page.s_ele()
            ele_divs = s_ele.eles(self.asin_locator)  # 先获取所有asin数量
            self.cur_info['page_count'] = self.cur_info['page_count'] or len(ele_divs)
            asins_list = ele_divs.filter.get.attrs('data-asin')
            if asins_list:
                api_result = sse.get_list(site, asins_list) or {}  # 源数据
                break
            self.wait_page_loaded(60)
            if _wait_asin > 2:
                self.page.refresh()
                self.page.wait.load_start(raise_err=False)
        else:
            api_result = {}
            asins_list = []
            ele_divs = []
            self.cur_info['result'] += f' |[第{self.cur_info["cur_page"]}页]页面元素获取失败'
            logging(f'[第{self.cur_info["cur_page"]}页]页面元素获取失败')
            raise Exception('页面元素获取失败')

        api_items = (api_result.get('data') or {}).get('items') or []  # ["data"]["items"]
        api_data_dict = {item.get('asin') or '': item for item in api_items} or {}  # 取asin组成键值对
        if not api_data_dict:
            self.cur_info['result'] += f' |[第{self.cur_info["cur_page"]}页]插件数据获取失败：{api_result}'
            self.cur_info['status'] = 20
            logging(self.cur_info['result'])
            fsmsg.send('亚马逊选品任务采集', f'卖家精灵接口获取失败 asins：{asins_list}', self.cur_info['result'])
        # 遍历每个元素，提取其中的数据
        for c_i, div in enumerate(ele_divs):
            data = {}
            try:
                # 从元素中提取ASIN、标题、评分、评论数、价格、图片URL等信息
                asin = div.attr('data-asin').strip()
                title = div.ele('t:h2').text
                e_score = div.ele('.a-icon-alt')
                stars = e_score.text.split()[0].replace(',', '.') if e_score else 0
                e_comment_num = div.ele('c=[class="a-size-base s-underline-text"]')
                ratings = e_comment_num.text.replace(',', '') if e_comment_num else 0
                e_price = div.ele('c=[class="a-offscreen"]')
                if not e_price:
                    continue
                symbol, price = separate_currency_price(e_price.text)
                currency = AmazonConfig.CURRENCYS.get(symbol, f'未识别{symbol}')
                image = div.ele('c=img.s-image').attr('src')

                asin_datas = api_data_dict.get(asin) or {}
                seller_type = asin_datas.get('sellerType') or asin_datas.get('seller_type') or ''
                sellers = asin_datas.get('sellers') or 0
                seller_id = asin_datas.get('seller_id') or ''
                # seller_url = f'https://www.amazon.{get_key_by_value(site, AmazonConfig.COUNTRY_SITE_SPRITE)}/sp?seller={seller_id}' if seller_id else ''
                seller_url = f"{'/'.join(self.page.url.split('/', )[:3])}/sp?seller={seller_id}" if seller_id else ''

                bsrList = asin_datas.get('bsrList') or []
                if bsrList:
                    brs_json = [
                        {'code': int(br.get('id')) if br.get('id').isdigit() else 0,
                         'rank': br.get('rank') or 0,
                         'label': br.get('label') or ''}
                        for br in bsrList if br.get('id').isdigit()
                    ]
                    # 使用 min 函数找到 rank 最小的元素，max 找到最大的
                    min_rank_br = min(brs_json, key=lambda x: x['rank'], default={})
                    max_rank_br = max(brs_json, key=lambda x: x['rank'], default={})

                    # 更新 data 字典，仅当存在多个元素时才更新小类信息
                    data.update({
                        'category_id': min_rank_br.get('code', ''),
                        'category_name': min_rank_br.get('label', ''),
                        'bsr_rank': min_rank_br.get('rank', 0),
                        # 只有当 brs_json 包含多于一个元素时才设置小类信息
                        'subcategory': max_rank_br.get('label', '') if len(brs_json) > 1 else '',
                        'subcategory_rank': max_rank_br.get('rank', '') if len(brs_json) > 1 else '',
                        'subcategory_code': max_rank_br.get('code', '') if len(brs_json) > 1 else '',
                    })
                else:
                    brs_json = []
                    e_brs = div.eles(f'c=div[data-asin^="{asin}"] .bsr-list-item', timeout=2)
                    if e_brs:
                        for e_b in e_brs:
                            brs_label = e_b.child(2).text.strip()
                            rank = extract_number(e_b.child().child().text.replace(',', ''))
                            brs_json.append({'label': brs_label, 'rank': rank})
                        # if len(brs_json) >= 2:
                        data['category_name'] = brs_json[0].get('label', '')
                        data['bsr_rank'] = brs_json[0].get('rank', '')
                        data['subcategory'] = brs_json[-1].get('label', '')
                        data['subcategory_rank'] = brs_json[-1].get('rank', '')
                brs_json_str = json.dumps(brs_json, ensure_ascii=False, separators=(",", ":"))
                # 将提取到的信息存储到字典中
                data['asin'] = asin
                data['title'] = title
                data['stars'] = stars
                data['ratings'] = ratings
                data['price'] = price
                data['currency'] = currency
                data['image'] = image
                data['seller_type'] = seller_type
                data['sellers'] = sellers
                data['seller_url'] = seller_url
                data['brs_json_str'] = brs_json_str
                data['url'] = f"{'/'.join(self.page.url.split('/', )[:3])}/dp/{asin}?psc=1"
                # data['url'] = f'https://www.amazon.{AmazonConfig.get_amz_site(site)}/dp/{asin}?psc=1'
                data['brand'] = asin_datas.get('brand') or ''
                data['seller_name'] = asin_datas.get('sellerName') or asin_datas.get('seller_name') or ''
                data['units'] = asin_datas.get('units') or 0
                data['month_units'] = asin_datas.get('month_units') or 0
                data['amount'] = asin_datas.get('amount') or 0
                data['variations'] = asin_datas.get('variations') or 0
                data['weight'] = asin_datas.get('weight') or ''
                data['dimension'] = asin_datas.get('dimension') or ''
                available = asin_datas.get('available') or 0
                data['available'] = int(available) / 1000 if len(str(available)) == 13 else available

                data['parent'] = asin_datas.get('parent') or ''
                data['fba'] = asin_datas.get('fba') or ''
                data['profit'] = asin_datas.get('profit') or ''
                data['seller_location'] = (asin_datas.get('sellerDto') or {}).get('nationCode') or ''
                datas.append(data)
            except Exception as e:
                # 如果在解析过程中遇到异常，打印异常信息
                traceback.print_exc()
                print(e)
        # 返回包含所有数据的列表
        return datas

    def get_search_image(self, content):
        # 是否二进制类型
        if not isinstance(content, bytes):
            with open(content, 'rb') as f:
                img_data = f.read()
        else:
            img_data = content
        seatch_img = 'https://www.amazon.de/stylesnap?q=local'
        # seatch_img = 'https://www.amazon.com/shopthelook?q=local'

        search_headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'origin': 'https://www.amazon.de',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'rtt': '350',
            'sec-ch-device-memory': '8',
            'sec-ch-dpr': '1',
            'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"10.0.0"',
            'sec-ch-viewport-width': '1912',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0',
            'viewport-width': '1912',
        }
        self.page.get(seatch_img)
        self.check_page()
        stylesnap = self.page('@name=stylesnap').attr('value')
        if not stylesnap:
            print('未获取到stylesnap')
            return None
        stylesnapToken = quote(stylesnap)
        params = {
            'stylesnapToken': stylesnapToken,
        }
        files = {
            'explore-looks.jpg': ('explore-looks.jpg', img_data, 'image/jpeg')
        }

        res = requests.post(
                'https://www.amazon.de/stylesnap/upload',
                params=params,
                cookies=self.page.cookies().as_dict(),
                headers=search_headers,
                timeout=30,
                files=files)
        if 'searchResults' in res.text:
            datas = res.json()
        else:
            # print('搜索出错')
            with open('error.html', 'w', encoding='utf-8') as f:
                f.write(res.text)
            datas = None
        return datas

    def get_detail_info(self, site, asin, is_get_pic=False):
        site = AmazonConfig.get_amz_site(site)
        url = f'https://www.amazon.{site}/dp/{asin}?th=1'
        self.page.get(url)
        self.check_page()

        img_url = title = description = ''
        about_item = ['', '', '', '', '', '']
        if 'ref=cs_404_link' in self.page.html:
            print('asin所在网站访问失败！')
            return 'asin所在网站访问失败！', title, description, about_item
        try:
            # 主图
            img = self.page('c=div#imgTagWrapperId>img')
            img_url = img.attr('src')

            if is_get_pic:
                # 所有产品图片
                all_pics = re.findall(',"large":"(.*?)"', self.page.html)  # 使用正则表达式匹配JSON字符串

                if all_pics:
                    os.makedirs(f'{self.output_folder}', exist_ok=True)
                    for i, pic_url in enumerate(all_pics, start=1):
                        file_name = f'{self.output_folder}\\{asin}_{i}.jpg'

                        if not download_image(pic_url, file_name=file_name):
                            print(f'第{i}张产品图片下载失败')
                else:
                    print(f'{url} 获取产品图片链接出错')

            # 标题
            title = self.page('c=span#productTitle').text

            # 五点
            about_items = self.page.eles('c=div#feature-bullets>ul>li')
            for i, item in enumerate(about_items):
                if i > 5:
                    break
                about_item[i] = about_items[i].text.strip()

            # 产品描述
            try:
                description = self.page('c=div#productDescription', timeout=3)
                description = description.text if description else '暂无'
            except:
                print(f'获取产品描述失败,url：{url}')
                description = '暂无'

        except Exception as e:
            print(e)
            print(f'{asin} Listing获取出错')
        return img_url, title, description, about_item

    def translate_bing(self, contents):
        tab = self.page.new_tab('https://cn.bing.com/translator?ref=TThis&text=&from=auto&to=zh-Hans')
        content_zhs = contents
        try:
            tab('#tta_input_ta')
            for i, text in enumerate(contents):
                try:
                    if not text or '暂无' == text:
                        content_zhs[i] = text
                        continue

                    tab.refresh()
                    tab.wait.load_start(raise_err=False)
                    # tab.wait.load_start(raise_err=False)
                    text = remove_non_bmp_chars(text)

                    for _ in range(5):
                        tab.refresh()
                        tab.wait.load_start(raise_err=False)
                        # tab.wait.load_start(raise_err=False)
                        element_input1 = tab('#tta_input_ta')  # 原文输入框
                        if element_input1.value.strip() == '':
                            element_input1 = tab('#tta_input_ta')  # 原文输入框
                            element_input1.clear()
                            element_input1.input(text + '\n')
                            try:
                                tab.wait.ele_displayed('.tta_output_hastxt', timeout=5)
                                break
                            except Exception as e:
                                print(f"{e},即将重试第{_ + 1}次")
                        else:
                            break
                    else:
                        print('翻译出错超过最大次数5')
                    time.sleep(1)

                    element_input2 = tab('#tta_output_ta')  # 译文输入框
                    result_text = element_input2.value.strip()
                    content_zhs[i] = result_text
                except Exception as e:
                    print(e)
                    content_zhs[i] = '翻译出错'
        except Exception as e:
            print(e)
        tab.close()
        return content_zhs

    def get_detail_info_sales_status(self, param):
        urls = []
        if isinstance(param, str):
            urls.append(param)
        elif isinstance(param, list):
            urls = param
        else:
            raise TypeError("无效的输入类型，请输入字符串或列表")
        data = []
        for index, url in enumerate(tqdm(urls, desc='获取详情页信息', unit='页')):
            rating = '获取失败'
            comment_num = '获取失败'
            buy_state = '获取失败'
            try:
                self.page.get(url)
                if not self.check_page():
                    logging(f'{url} 页面异常!')
                    data.append([rating, comment_num, buy_state])
                    continue
                s_ele = self.page.s_ele()

                rating_ = s_ele('c=#acrPopover [class="a-size-base a-color-base"]', timeout=2)
                if rating_:
                    rating = rating_.text.strip()
                elif s_ele('c=#cm-cr-dp-review-header > h3 > span', timeout=2):
                    rating = '暂无'
                    comment_num = '暂无'

                comment_num_ = s_ele("c=#acrCustomerReviewText", timeout=2)
                if comment_num_:
                    comment_num = comment_num_.text.split(" ")[0]

                buy_state_ = s_ele('c=[id="atc-declarative"]', timeout=2)
                if buy_state_:
                    buy_state = '可售'
                else:
                    buy_state = '不可售'
            except Exception as e:
                print(e)
                print(f'{url} 获取失败!')
            data.append([rating, comment_num, buy_state])
        return data

    def check_asin_listing(self, asins, site, app_id=5):
        self.cur_info = init_fetch_info()
        datas = []
        asin_list = split_get_list(asins)
        if not asin_list:
            self.cur_info['finally_result'] += f' |无效的输入类型，asins请输入字符串或列表'
            self.cur_info['finally_status'] = 20
            return datas
        asin_list = [asin for asin in asin_list if asin]
        for index, asin in enumerate(tqdm(asin_list, desc='检查详情页状态', unit='页', file=sys.stdout)):
            if len(asin.strip()) > 10:
                self.cur_info['finally_result'] += f' |无效的输入类型，asin长度不能超过10位'
                self.cur_info['finally_status'] = 20
                return datas
            row = {
                'asin_status': 2  # '获取异常'
            }
            site_code = AmazonConfig.get_amz_site(site)

            url = f'https://www.amazon.{site_code}/dp/{asin}'
            self.cur_info['cur_asin_result'] = ''
            self.page.get(url, timeout=60, retry=self.proxy_retry)

            @retry_on_exception(msg=f'[{asin} {site}]抓取异常。')
            def get_data(self):
                if '404页面' in self.cur_info['cur_asin_result']:
                    row['asin_status'] = 99  # '被删除' 下架
                self.reset_language()
                # 标题
                title = self.page('c=span#productTitle').text
                if title:
                    row['asin_status'] = 10  # '正常' 可售
                row['title'] = title
                if 'id="atc-declarative"' not in self.page.html:
                    row['asin_status'] = 20  # '不可售'
                e_seller = self.page('c=#merchantInfoFeature_feature_div .offer-display-feature-text.a-spacing-none ')
                if e_seller:
                    row['platform_account'] = e_seller.text

                if app_id == 8:
                    # 五点
                    items = self.page.eles('c=div#feature-bullets ul li span', timeout=3)
                    if not items:
                        items = self.page.eles('c=div#productFactsDesktopExpander ul li span', timeout=3)
                    for i1, item in enumerate(items):
                        row[f'short_desc{i1 + 1}'] = item.text
                    # 产品描述
                    try:
                        # detail = self.page('c=div#detailBulletsWrapper_feature_div', timeout=3).text or '暂无'
                        detail = self.page('c=div#productDescription', timeout=3) or self.page('c=div#aplus', timeout=3)
                        detail = detail.text.strip() or '暂无'
                    except:
                        # tqdm.write(f'获取详情失败,url：{url}')
                        detail = '未找到'
                    row['detail'] = detail
                    # tqdm.write(f'正在检索流量词...')
                    keywordCns = xyzc.fetch_search_terms(asin, site)
                    row['keywords'] = keywordCns

            get_data(self)
            row.update({
                'site': site,
                'asin': asin,
                'has_cart': 1 if row['asin_status'] == 10 else 0,
                'url': url,
            })
            datas.append(row)
            if len(datas) % 100 == 0:  # 每100个存一次数据库
                # print(f"达到 {len(datas)} 个元素,执行额外处理！")
                # print(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
                insert_amazon_listing(datas, self.task_params, 'data_amazon_asin_attr')

        self.cur_info['result'] = '成功' if self.cur_info['finally_status'] == 10 else self.cur_info['finally_result']
        self.cur_info['status'] = self.cur_info['finally_status']
        return datas

    def reset_language(self, tab=None, site=None):
        tab = tab or self.page
        cur_url, cur_site = self.get_url_info(tab)
        site = site or cur_site
        site_code = get_key_by_value(site, AmazonConfig.COUNTRY_SITE_SPRITE)
        language = AmazonConfig.LANGUAGES.get(site_code, '')
        new_url = update_url_param(cur_url, 'language', language) if language else cur_url
        cookies_list = tab.cookies()
        lc_ = next(filter(lambda x: x['name'].startswith('lc-'), cookies_list), {})
        if lc_ and site_code.lower() not in lc_['value'].lower():
            tab.set.cookies.remove(name=lc_['name'], domain=lc_['domain'])
            tab.get(new_url)

    def get_url_info(self, tab=None):
        tab = tab or self.page
        cur_url = tab.url
        domain = urlparse(cur_url).netloc
        site = domain.split('.')[-1]
        return cur_url, site


def init_fetch_info():
    return {
        'result': '完成', 'status': 10, 'finally_result': '', 'finally_status': 10, 'page_count': 0, 'end_page_num': 0, 'total': 0, 'cur_page': 1, 'real_page': 1,
        'uniq_fetch_id': set(), 'all_loc_index': 0
    }


def download_image(url, file_name):
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(file_name, 'wb') as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)
        return os.path.abspath(file_name)
    else:
        return None


def date_to_timestamp(t):
    # t = '2023-05-31(379天)'
    # 提取日期部分
    date_str = t.split('(')[0].strip()
    # 将字符串转换为datetime对象
    base_date = datetime.strptime(date_str, '%Y-%m-%d')
    # 转换为时间戳
    timestamp = int(base_date.timestamp())
    return timestamp


def insert_or_update_amazon(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """

    # 处理亚马逊数据表
    # 从params字典中获取相关参数
    app_id = params.get('app_id') or 0
    task_id = params.get('task_id') or 0
    date_time = params.get('date_time') or 0
    task_time = params.get('task_time') or now_int()
    data_status = params.get('data_status') or 0
    user_id = params.get('user_id') or 0
    username = params.get('username') or ''
    site = params.get('site') or ''
    create_time = params.get('create_time') or now_int()
    task_num = params.get('task_num') or 0
    platform_num = params.get('platform_num') or 0
    done_time = params.get('done_time') or 0

    status = max(params.get('finally_status') or 0, params.get('status') or 0) or 10
    result = params.get('result') or '成功'

    run_num = 1

    # 定义用于插入或更新Amazon数据的SQL语句,确保unique_id列是数据库表中的唯一键或主键，以利用ON DUPLICATE KEY UPDATE特性
    sql_upsert_amazon = """
        INSERT INTO `data_amazon_asin`(
            `unique_id`,`app_id`,`task_id`,`datetime`,`task_time`,`data_status`,`user_id`,`username`,`platform_account`,`site`,`asin`,`image`,`title`,`price`,`currency`,`url`,`create_time`
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            `app_id`=VALUES(`app_id`),
            `task_id`=VALUES(`task_id`),
            `datetime`=VALUES(`datetime`),
            `task_time`=VALUES(`task_time`),
            `data_status`=VALUES(`data_status`),
            `user_id`=VALUES(`user_id`),
            `username`=VALUES(`username`),
            `platform_account`=VALUES(`platform_account`),
            `site`=VALUES(`site`),
            `asin`=VALUES(`asin`),
            `image`=VALUES(`image`),
            `title`=VALUES(`title`),
            `price`=VALUES(`price`),
            `currency`=VALUES(`currency`),
            `url`=VALUES(`url`);
    """

    # 使用列表推导式准备插入数据的参数列表
    insert_params = [
        (
            item.get('asin') + '_' + site,  # 确保每个item都包含unique_id
            app_id,
            task_id,
            date_time,
            task_time,
            data_status,
            user_id,
            username,
            item.get('seller_name') or '',
            site,
            item.get('asin', ''),
            item.get('image', '') or '',
            item.get('title', ''),
            item.get('price', ''),
            item.get('currency') or '',
            item.get('url', ''),
            create_time,
        ) for item in data
    ]

    # 如果有数据需要插入，则执行插入操作
    if insert_params:
        # 执行插入操作
        rs = MS.insert_many(sql_upsert_amazon, insert_params)
        # 根据插入结果设置状态和结果信息
        f_status, f_result = (10, '成功') if rs > 0 else (10, '无新增或需要更新的数据') if rs == 0 else (20, '异常')
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID-{task_id};抓取{task_num}条数据;操作{rs}条{result}')
    else:
        f_status, f_result = (10, '无数据入库')

    if f_status == 20:
        if MS.err:
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send('亚马逊数据采集', msg, MS.err)

    if f_status == 20:
        result += f' |入库提示:{f_result}'
        status = 20
    elif f_status == 10 and '成功' not in f_result:
        result += f' |入库提示:{f_result}'

    # 更新任务状态
    MS.update(
            f'update `task_goods` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


def insert_or_update_amazon_listing_old(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """

    # 处理亚马逊数据表
    # 从params字典中获取相关参数
    app_id = params.get('app_id') or 0
    task_id = params.get('task_id') or 0
    date_time = params.get('date_time') or 0
    task_time = params.get('task_time') or now_int()
    data_status = params.get('data_status') or 0
    user_id = params.get('user_id') or 0
    username = params.get('username') or ''
    site = params.get('site') or ''
    create_time = params.get('create_time') or now_int()
    task_num = params.get('task_num') or 0
    platform_num = params.get('platform_num') or 0
    done_time = params.get('done_time') or 0

    status = max(params.get('finally_status') or 0, params.get('status') or 0) or 10
    result = params.get('result') or '成功'

    run_num = 1

    # 定义用于插入或更新Amazon数据的SQL语句,确保unique_id列是数据库表中的唯一键或主键，以利用ON DUPLICATE KEY UPDATE特性
    sql_upsert_amazon = """
        INSERT INTO `data_amazon_asin_attr`(
            `unique_id`,`app_id`,`task_id`,`datetime`,`task_time`,`data_status`,`user_id`,`username`,`site`,`asin`,`status`,`has_cart`,`url`,`create_time`,`title`,`keywords`,`short_desc1`,`short_desc2`,`short_desc3`,`short_desc4`,`short_desc5`,`detail`,`platform_account`
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE`app_id`=VALUES(`app_id`),
            `task_id`=VALUES(`task_id`),
            `datetime`=VALUES(`datetime`),
            `task_time`=VALUES(`task_time`),
            `data_status`=VALUES(`data_status`),
            `user_id`=VALUES(`user_id`),
            `username`=VALUES(`username`),
            `site`=VALUES(`site`),
            `asin`=VALUES(`asin`),
            `status`=VALUES(`status`),
            `has_cart`=VALUES(`has_cart`),
            `url`=VALUES(`url`),
            `title`=VALUES(`title`),
            `keywords`=VALUES(`keywords`),
            `short_desc1`=VALUES(`short_desc1`),
            `short_desc2`=VALUES(`short_desc2`),
            `short_desc3`=VALUES(`short_desc3`),
            `short_desc4`=VALUES(`short_desc4`),
            `short_desc5`=VALUES(`short_desc5`),
            `platform_account`=VALUES(`platform_account`),
            `detail`=VALUES(`detail`);
    """

    # 使用列表推导式准备插入数据的参数列表
    insert_params = [
        (
            item.get('asin') + '_' + (site or item.get('site') or ''),  # 确保每个item都包含unique_id
            app_id,
            task_id,
            date_time,
            task_time,
            2 if item.get('asin_status') == 2 else 1,
            user_id,
            username,
            site or item.get('site') or '',
            item.get('asin'),
            item.get('asin_status') or 0,
            item.get('has_cart') or 0,
            item.get('url') or '',
            create_time,
            item.get('title') or '',
            item.get('keywords') or '',
            item.get('short_desc1') or '',
            item.get('short_desc2') or '',
            item.get('short_desc3') or '',
            item.get('short_desc4') or '',
            item.get('short_desc5') or '',
            item.get('detail') or '',
            item.get('platform_account') or '',
        ) for item in data
    ]

    # 如果有数据需要插入，则执行插入操作
    if insert_params:
        # 执行插入操作
        rs_num = MS.insert_many(sql_upsert_amazon, insert_params)
        # 根据插入结果设置状态和结果信息
        if MS.err:
            f_status, f_result = (20, '异常')
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send('亚马逊Listing数据采集', msg, MS.err)
            result += f' |入库提示:{f_result}'
            status = 20
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID:{task_id};抓取{task_num}条数据;操作{rs_num}条{result}') if task_num else ''
    else:
        result += f' |入库提示:无数据入库'

    # 更新任务状态
    MS.update(
            f'update `task_listing` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


# is_test = is_test_environment()
is_test = False
pin = Pinner(False, is_test)

if __name__ == "__main__":
    amazon = AmazonDP(proxy_type=1)
    url = 'https://www.amazon.co.uk/s?rh=n%3A20135092031&fs=true'
    url = 'https://www.amazon.fr/s?k=protection+%C3%A9cran+s24&page=1'
    pages = 3
    rs = amazon.get_asins_loc_index(url, pages)
    # rs = amazon.get_asins_by_url_pages(url, pages)
    # rs = amazon.check_asin_listing('B0D4LWR6DZ', 'fr')
    print(amazon.cur_info)
    print(f'平台数量：{amazon.cur_info["total"]},抓取数量：{len(rs)}')
