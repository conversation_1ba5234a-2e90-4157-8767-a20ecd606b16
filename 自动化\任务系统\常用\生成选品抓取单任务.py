from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

MS = MSXS
# 读取所有Sheet页
sheets = pd.read_excel(r'D:\Downloads\新-关键词采集-0814-23个IP.xlsx', header=None, sheet_name=None)

# 获取sheet名
sheets = dict(sheets)
# 打印每个Sheet页的内容
unique_id = []
success_count = 0
total = 0
for sheet_name, df in sheets.items():
    print(f"--- Sheet: {sheet_name} ---")
    keywords = df[0].to_list()
    total += len(keywords)
    for index, row in enumerate(keywords, start=1):
        k = row.strip().replace(' ', '+')
        url = f'https://www.amazon.de/s?k={k}'
        title = f'{sheet_name}_{index}'
        if k in unique_id:
            print(f'{k} {title} 已存在')
            continue
        # print(title, url)
        unique_id.append(k)

        if MS.get_one(f"select * from `task_goods` where `title` = '{title}' and `url` = '{url}'"):
            print(f'{title} 已存在 跳过')
            continue

        sql = f"call CreateTaskGoods(1,'amazon', 0, 'de', '{title}', '{url}', 18, '曹智聪', 10, 0);"
        rs = MS.update(sql)
        if rs:
            print(f'{title} 插入成功')
            success_count += 1
    print(f'成功插入 {success_count} 条数据')
print(f"最终成功插入 {success_count} 条数据；总数：{total}")
