# -*- coding:UTF-8 -*-
# @FileName  :亚马逊asins搜图价格.py
# @Time      :2024/12/30 15:00
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.亚马逊.Amazon import *
import time

tb_name = 'rpa.task_asins_price'


def main():
    # 获取主任务
    main_tasks = MS.get_dict(
            f'SELECT id, app_id, contents, user_id, username, datetime, site, page, title  FROM {tb_name} WHERE STATUS = 1 ORDER BY priority desc, id asc'
    )
    if not main_tasks:
        time.sleep(10)
        return
    for task in main_tasks:
        task_id = task.get('id')
        # 修改所关联任务状态为执行中
        if MS.update(f'UPDATE {tb_name} SET status = 2,`execute_time` = %s WHERE id = %s and `status` = 1', (now_int(), task_id)) < 1:
            logging(f'当前任务已经在执行')
            return False
        app_id = task.get('app_id')
        contents = task.get('contents')
        user_id = task.get('user_id')
        username = task.get('username')
        task_time = task.get('task_time')  # 关联任务的所属时间`datetime`
        site = task.get('site')
        page = task.get('page')
        title = task.get('title')
        params = {  # 更新任务表/数据表记录的字段
            'task_id': task_id,
            'app_id': app_id,
            'user_id': user_id,
            'username': username,
            'task_time': task_time,
            'date_time': get_today_zero_timestamp(),  # 当天0点时间戳
            'site': site,
        }
        # 开始抓取任务

        logging(f'任务ID:{task_id}, 当前应用ID:{app_id}, title:{title}')
        amazon.task_params = params
        result_data = amazon.fetch_search_image_price(contents, site)  # 抓取数据

        task_num = len(result_data)  # 任务数量
        data_status = 1 if task_num > 0 else 3  # 数据状态
        params['create_time'] = now_int()
        params['platform_num'] = task_num
        params['task_num'] = task_num
        params['data_status'] = data_status
        params['done_time'] = now_int()
        params['result'] = amazon.cur_info['result']
        params['status'] = amazon.cur_info['status']
        insert_or_update_amazon_search_data_by_image(result_data, params)  # 更新数据表

        logging(f'任务:{task_id} 已执行结束')
        print()


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')

    amazon = AmazonListing(proxy_type=1) if not is_test_environment() else AmazonListing(proxy_type=1, port=9555)
    while True:
        try:
            main()
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send(None, msg, err)
