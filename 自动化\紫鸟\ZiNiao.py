# -*- coding:UTF-8 -*-
import pdb

from utils_mrc.pub_fun import *
import base64
import os
import re
import shutil
import time
import traceback
import json
import dateutil.parser
import psutil
import requests
import uuid
import concurrent.futures
import pymsgbox
import subprocess
import DrissionPage.errors
from dateutil.parser import parse
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage
from urllib.parse import urlparse
from DrissionPage.common import wait_until
from utils_mrc.MysqlHelper import *
from utils_mrc.FeiShuAPI import *
from tqdm import tqdm
from requests.exceptions import ConnectionError
from urllib3.exceptions import NewConnectionError
from PIL import Image
from io import BytesIO
from work.自动化.紫鸟.GoodDiyRequestBodyBuilder import GoodDiyRequestBodyBuilder, get_template_identifier

site_map = {
    '法国': 'fr',
    '西班牙': 'es',
    '意大利': 'it',
    '德国': 'de',
    '比利时': 'be',
    '波兰': 'pl',
    '荷兰': 'nl',
    '英国': 'uk',
    '瑞典': 'se',
    '美国': 'us',
    '加拿大': 'ca',
    '墨西哥': 'mx',
}

pdf_temp_download_path = r'D:\rpa_download\pdf\payment_reports_pdf'
invoice_download_path = r'D:\rpa_download\pdf\invoice'


class SuperBrowserClient:
    def __init__(self, exe_path=None, socket_port=None):
        self.exe_path = exe_path or r"D:\软件\SuperBrowser\starter.exe"  # 客户端程序starter.exe的路径
        self.cur_port = find_ziniao_port()
        self.socket_port = socket_port or self.cur_port or get_available_port()
        self.init()

    def init(self):
        if not self.cur_port:
            # 终止已启动的进程
            self.kill_process()
            print("=====启动客户端=====")
            self.start()
        return self.socket_port

    def reboot(self, socket_port=None):
        self.socket_port = socket_port or get_available_port()  # 系统未被占用的端口

        print(f"=====重启客户端 端口号:{self.socket_port}=====")
        # 终止已启动的进程
        self.kill_process()
        self.start()
        return self.socket_port

    @staticmethod
    def kill_process():
        """终止已运行的紫鸟客户端进程。"""
        # os.system('taskkill /f /t /im SuperBrowser.exe')
        try:
            result = subprocess.run(['taskkill', '/f', '/t', '/im', 'SuperBrowser.exe'], capture_output=True, text=True, encoding='gbk')
            if result.stdout: print("标准输出:", result.stdout)
            if result.stderr: print("标准错误:", result.stderr)
        except Exception as e:
            print(f"发生错误: {e}")
        time.sleep(2)

    def start(self):
        """启动紫鸟客户端。"""
        try:
            cmd = [
                self.exe_path.encode('utf-8'),
                '--run_type=web_driver',
                '--ipc_type=http',
                f'--port={self.socket_port}'
            ]
            subprocess.Popen(cmd)
            print('=====紫鸟客户端启动指令已发送=====')
            time.sleep(20)
            self.cur_port = self.socket_port
        except:
            err_msg = '启动浏览器进程失败: ' + traceback.format_exc()
            print(err_msg)
            fsmsg.send(None, err_msg)
            exit()


class ZiNiaoDP:
    def __init__(self, browser_info):
        self.cur_info = {
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'cur_page': 1,  # 当前页
            'real_page': 0,  # 已操作的页数
            'items_per_page': 0,  # 单页数量
            'total_items': 0,  # 总数量
            'total_pages': 0,  # 总页数
            'site': '',
            'siteCN': '',
            'fail_data': [],
        }
        self.home_url = ''
        self.site_info = {}
        self.country_info = {}
        self.cur_site_info = {}
        self.browser_info = browser_info
        self.store_name = self.browser_info.get("browserName")
        self.page = self.get_driver()
        self.sess = SessionPage()
        self.sess.set.retry_times(3)
        self.sess.set.retry_interval(3)
        self.home_url = browser_info.get('launcherPage')
        upe = urlparse(self.home_url)
        self.base_url = f'{upe.scheme}://{upe.netloc}' or 'https://sellercentral.amazon.co.uk'
        self.ads_base_url = f'{upe.scheme}://{upe.netloc}'.replace('sellercentral', 'advertising') or 'https://advertising.amazon.co.uk'

    def get_driver(self):
        """获取Chromium页面的驱动实例。"""
        if self.browser_info.get('core_type') == 'Chromium':
            port = self.browser_info.get('debuggingPort')
            browser_path = self.browser_info.get('browserPath')
            co = ChromiumOptions()
            co.set_browser_path(browser_path)
            co.set_local_port(port)
            co.set_retry(3)
            co.set_argument('--log-level=3')
            self.page = ChromiumPage(addr_or_opts=co)
            print(f'@@ 启动浏览器驱动成功！关联店铺名：【{self.store_name}】。端口：{port}')
            return self.page
        # print('打开浏览器驱动失败！')
        return None

    def open_ip_check(self, ip_check_url):
        """检查IP是否有效。"""
        try:
            self.page.get(ip_check_url)
            self.page.ele('xpath://*[contains(@class, "ant-btn ant-btn-default styles_btn--success")]')
            return True
        except DrissionPage.errors.ElementNotFoundError:
            print("未找到IP检测成功元素")
            return False
        except Exception as e:
            print("IP检测异常: " + traceback.format_exc())
            return False

    def check_page(self, tab=None):
        """检查当前页面。"""
        tab = tab or self.page
        for check_count in range(5):
            time.sleep(1)
            cur_url = tab.url
            try:
                if any(x in tab.url for x in ['supportRetry', 'error']):
                    tab.get(self.home_url)
                    continue
                if 'id="auth-error-message-box"' in tab.html:
                    error_msg = tab('#auth-error-message-box').text
                    if 'OTP' in error_msg:
                        continue
                    self.cur_info['finally_result'] += f'|{self.store_name} 账号验证失败：{error_msg}'
                    self.cur_info['finally_status'] = 20
                    return False
                if 'authorization/select-account' in tab.url or 'picker-switch-accounts-button' in tab.html:
                    cur_site = tab('c=#partner-switcher span').text.split('|')[-1].strip()
                    btn = tab(f'tx={cur_site}')
                    if btn: btn.click(by_js=True)
                    t = tab('c=.full-page-account-switcher-footer >button')
                    btnS = tab('.picker-switch-accounts-button', index=-1)
                    btnS.click(by_js=True) if btnS else ''
                    time.sleep(2)
                    continue
                if 'account-switcher/default/' in tab.url and 'full-page-account-switcher-footer' in tab.html:
                    btn = tab('c=.full-page-account-switcher-account-details', index=-1)
                    if btn: btn.click(by_js=True)
                    btnS = tab('c=.full-page-account-switcher-footer >button', index=-1)
                    if btnS: btnS.click(by_js=True)
                    time.sleep(2)
                    continue
                if 'authorization/failed/' in cur_url:
                    cur_url = self.page.url
                    rs = re.findall(r'authorization/failed/(.*?)\?', cur_url)
                    err_url_path = rs[0] if rs else cur_url
                    self.cur_info['result'] = f'|店铺异常待处理：【{self.store_name}】 站点：{self.cur_info.get("siteCN")} url关键词： {err_url_path}'
                    self.cur_info['finally_result'] += self.cur_info['result']
                    # self.cur_info['finally_status'] = 20
                    # logging(self.cur_info['result'])
                    return False
                if 'ap/forgotpassword/reverification' in tab.url:
                    self.cur_info['result'] = f'|店铺异常待处理：【{self.store_name}】 需要重置密码!'
                    self.cur_info['finally_result'] += self.cur_info['result']
                    # self.cur_info['finally_status'] = 20
                    return False
                if 'ap/signin' in cur_url:
                    print('登录验证页')
                    if 'Switch accounts' in tab.html:
                        print('选择账号页')
                        btn = tab('@data-test-id=customerName', timeout=2)
                        btn.click() if btn else ''
                        time.sleep(1)
                    if 'id="signInSubmit"' in tab.html:
                        time.sleep(2)
                        tab('#signInSubmit', timeout=2).click()
                    elif 'id="continue"' in tab.html:
                        time.sleep(2)
                        tab('#continue', timeout=2).click()
                    time.sleep(2)
                    continue
                if 'ap/mfa' in cur_url:
                    print('两步验证页')
                    if 'auth-mfa-otpcode' in tab.html:
                        try:
                            wait_until(lambda: tab('#auth-mfa-otpcode').value, timeout=10)
                        except TimeoutError:
                            err_msg = f'|【{self.store_name}】未能获取验证码，请处理！'
                            self.cur_info['finally_result'] += err_msg
                            self.cur_info['finally_status'] = 20
                            return False
                        tab('#auth-signin-button').click()
                    elif 'auth-send-code' in tab.html:
                        tab('#auth-send-code').click()
                    elif 'auth-send-code' in tab.html:
                        tab('#auth-send-code').click()
                    elif submit := tab('@type=submit', timeout=2):
                        submit.click()
                    time.sleep(2)
                    continue
                if 'authportal-main-section' in tab.html:
                    ele_main = tab('#authportal-main-section')
                    err_msg = f'|【{self.store_name}】 未处理的新验证页面!网页内容为： {ele_main.text}！'
                    self.cur_info['finally_result'] += err_msg
                    self.cur_info['finally_status'] = 20
                    logging(err_msg)
                    return False
                if 'ap/signin' not in tab.url and 'ap/mfa?' not in tab.url and 'authportal-main-section' not in tab.html:
                    self.cur_info['status'] = 10
                    break
            except DrissionPage.errors.ContextLostError:
                time.sleep(2)
                continue
            except:
                traceback.print_exc()
        else:
            err_msg = f'|账号验证失败：【{self.store_name}】异常页面处理未通过，超过最大重试次数，请检查浏览器页面错误！'
            self.cur_info['finally_result'] += err_msg
            self.cur_info['finally_status'] = 20
            tab.get_screenshot(get_err_path(), f'{self.store_name}_{now_str()}')
            print(err_msg)
            return False
        return True

    def get_compliance_information(self):
        """获取商店的合规信息。"""
        result_datas = []
        curl = self.page.url
        upe = urlparse(curl)
        self.base_url = f'{upe.scheme}://{upe.netloc}'
        tag_url = f'{self.base_url}/spx/myc/dashboard'

        self.page.get(tag_url, timeout=45)
        if not self.check_page():
            return []
        self.page.get(tag_url, timeout=45) if 'spx/myc/dashboard' not in self.page.url else ''
        if not self.page('@data-cy=incompleteFilter'):
            self.page.refresh()
            self.page.wait.load_start()
        if self.get_grain_compliance_requirement_issues(status_data='ACTIVE'):
            self.page('@data-cy=incompleteFilter').click()  # 切换未完成列表
            result_datas.extend(self.parsed_rule_datas())
        if self.get_grain_compliance_requirement_issues(status_data='RESOLVED'):
            self.page('@data-cy=completeFilter').click()  # 切换已完成列表
            result_datas.extend(self.parsed_rule_datas())
        return result_datas

    def parsed_rule_datas(self):
        result_datas = []
        resolved_max_page = 10
        tab_title = '未完成的请求'
        try:
            wait_until(lambda: self.page('@ref=lbRecordCount') and '更多' not in self.page('@ref=lbRecordCount').text, timeout=30)
            lbRecordCount = convert_to_int(self.page('@ref=lbRecordCount', index=-1).text)
            lbCurrent = convert_to_int(self.page('@ref=lbCurrent', index=-1).text)
            lbTotal = convert_to_int(self.page('@ref=lbTotal', index=-1).text)

            if 'GRAIN_GMS_60DAYS' in self.page.html:  # '未完成的请求'
                status_data = 'ACTIVE'
                tab_title = '未完成的请求'
            else:  # '已完成的请求'
                status_data = 'RESOLVED'
                tab_title = '已完成的请求'
                if is_test_environment(): resolved_max_page = 0
                if resolved_max_page: lbTotal = min(lbTotal, resolved_max_page)

            self.cur_info['total_items'] += lbRecordCount
            self.cur_info['total_pages'] += lbTotal
            if not lbRecordCount:
                logging(f'|【{self.store_name}】 {tab_title}无数据')
                return result_datas
            keys = ['store', 'site', 'needs', 'brand', 'img', 'title', 'asin', 'sku', 'sales', 'status', 'remark', 'invalid']
            if not self.cur_info['site']:
                ele_site = self.page('tag:noscript@@text():fls-eu.amazon')
                if ele_site:
                    match = re.search(r'fls-eu.amazon.(.*?)/', ele_site.inner_html)
                    site = match.group(1).lower() if match else '未知站点'
                    self.cur_info['site'] = AmazonConfig.get_amz_site(site)
                    if self.cur_info['site'] not in set('UK、IT、DE、FR、ES'.lower().split('、')):
                        logging(f"{self.cur_info['site']}非目标站点，跳过")
                        return result_datas
            while lbCurrent <= lbTotal:
                lbFirstRowOnPage = convert_to_int(self.page('@ref=lbFirstRowOnPage', index=-1).text)
                lbLastRowOnPage = convert_to_int(self.page('@ref=lbLastRowOnPage', index=-1).text)
                cur_page_count = lbLastRowOnPage - lbFirstRowOnPage + 1
                self.page.ele('@data-cy=recallsCarousalItemImg', index=cur_page_count)
                spage = self.page.s_ele()
                table = spage('.ag-center-cols-container')
                rows = table.eles('@row-id')
                table_headers = self.page('@ref=eCenterContainer').eles('@role=columnheader')
                headers_dict = dict(zip(map(lambda x: x('@ref=eText').text if x('@ref=eText') else '', table_headers)
                                        , table_headers.get.attrs('col-id')))
                for row_index, row in enumerate(rows, start=1):
                    row_id = row.attr('row-id')
                    gcorId = re.findall('gcorId_(.*)_CPP', row_id)[0] if 'gcorId_' in row_id else ''
                    row_data = dict.fromkeys(keys, '')
                    row_data['store'] = self.store_name
                    row_data['site'] = self.cur_info.get('site', '')
                    cells = row.eles('@comp-id')
                    for i, cell in enumerate(cells):
                        try:
                            texts = cell.text.split('\n')
                            col_id = cell.attr('col-id')
                            if col_id == headers_dict.get('合规性要求') or col_id == 'complianceReq':
                                wait_until(lambda: texts)
                                needs = texts[0]
                                brand = texts[1]
                                row_data['needs'] = needs
                                row_data['brand'] = brand
                            elif col_id == headers_dict.get('受影响的商品') or col_id == '1':
                                wait_until(lambda: texts)
                                if len(texts) >= 3:
                                    tag_ele = cell('@data-cy=recallsCarousalItemImg')
                                    if tag_ele:
                                        img = tag_ele.attr('src')
                                        row_data['img'] = img
                                    title = texts[0]
                                    asin = texts[1].replace('ASIN:', '').strip()
                                    sku = texts[2].replace('SKU:', '').strip()
                                    row_data['title'] = title
                                    row_data['asin'] = asin
                                    row_data['sku'] = sku

                            elif col_id == headers_dict.get('存在风险的销售额') or col_id == 'GRAIN_GMS_60DAYS':
                                wait_until(lambda: texts)
                                sales = texts[0]
                                # 使用正则表达式移除sales字符串中的所有非数字字符
                                sales = re.sub('[^0-9]', '', sales)
                                row_data['sales'] = sales
                            elif col_id == headers_dict.get('合规状态') or col_id == 'ISSUE_CONSEQUENCE_DATE':
                                wait_until(lambda: texts)
                                remark = texts[0].strip()
                                row_data['remark'] = remark
                                if 'Due' in remark:
                                    try:
                                        # # 移除前缀 'Due ' 并转换为 datetime 对象
                                        # date_obj = datetime.strptime(remark[4:], '%b %d, %Y')
                                        # # 将 datetime 对象转换为时间戳
                                        # invalid = date_obj.timestamp()
                                        date_obj = dateutil.parser.parse(remark, fuzzy=True)
                                        invalid = int(date_obj.timestamp())
                                    except ValueError:
                                        invalid = 0
                                    row_data['invalid'] = invalid
                                tag_ele = cell('tag:kat-badge')
                                if tag_ele:
                                    status = tag_ele.attr('label')
                                    row_data['status'] = status
                        except Exception as e:
                            traceback.print_exc()
                            self.cur_info['result'] = f'|处理【{self.store_name}】 {self.cur_info["site"]} 第 {lbCurrent}页 {row_index} 行时发生错误: {e}'
                            self.cur_info['finally_result'] += self.cur_info['result']
                            self.cur_info['finally_status'] = 20
                            logging(self.cur_info['result'])

                    if not row_data['asin'] and 'aria-expanded' in row.html:
                        overlay_data_list = self.get_overlay_data_by_new_tab(gcorId, status_data)
                        for overlay_data in overlay_data_list:
                            copy_row = row_data.copy()
                            copy_row.update(overlay_data)
                            result_datas.append(copy_row)
                    else:
                        result_datas.append(row_data)
                # print(f'已处理 {len(result_datas)} 条数据')
                self.cur_info['real_page'] += 1
                if lbCurrent == lbTotal:
                    break
                lbCurrent += 1
                self.page('@ref=btNext', index=-1).click()
                time.sleep(2)
        except Exception as e:
            traceback.print_exc()
            self.cur_info['result'] = f'|【{self.store_name}】解析数据发生错误: {e}'
            self.cur_info['finally_result'] += self.cur_info['result']
            self.cur_info['finally_status'] = 20
            logging(self.cur_info['result'])
            self.page.get_screenshot(get_err_path(), f'{self.store_name}_{now_str()}')

        logging(f'【{self.store_name}】站点{self.cur_info["site"]} {tab_title} 爬取结束,总页数{self.cur_info["total_pages"]},总数量: {len(result_datas)}')
        self.cur_info['result'] = '' if self.cur_info['finally_status'] == 10 else self.cur_info['finally_result']
        self.cur_info['status'] = self.cur_info['finally_status']
        return result_datas

    def get_img_url_by_asin(self, asin):
        img_url = ''
        self.sess.set.cookies(self.page.cookies())
        old_url = f'{self.base_url}/spx/myc/myc-backend-service/api/get-product-images-by-asins?asins=B0D7RQGG1G'
        url = update_url_param(old_url, 'asins', asin)
        for _ in range(3):
            self.sess.get(url, retry=3)
            if 'asinImages' in self.sess.html:
                json_data = self.sess.json
                img_url = json_data.get('asinImages', {}).get(asin, {}).get('url') or ''
                break
            time.sleep(2)
        return img_url

    def get_overlay_data_by_new_tab(self, gcorId, status_data='ACTIVE'):
        data_list = []
        url = f"{self.base_url}/spx/myc/myc-backend-service/api/get-grain-compliance-requirement-issues?sellingPartnerType=SELLER&grainTypes=ASIN&grainTypes=SKU&pageOffset=1&pageSize=1000&searchBy=ASIN&searchValue=&sortBy=GRAIN_GMS_60DAYS&sortDirection=ASCENDING&&grainProgramNames=PRODUCT_ASSURANCE&grainProgramNames=RLPSAS&grainProgramNames=HazardousGoodsSeller&grainProgramNames=EPR_DE_WEEE&grainProgramNames=HazardousGoods&grainProgramNames=eu_gpsr_markeplace_regulation&grainProgramNames=COSS_PRODUCT_SAFETY&grainProgramNames=COSS_FOOD_SAFETY&grainProgramNames=COSS_DIETARY_SUPPLEMENTS&grainRequirementIssueStatuses={status_data}&&&vendorCode=&higherGrainType=gcorId&higherGrainValue=UN_BRANDED&issueIdentifier=CPP%7C100000%7Cresponsible_person"
        new_url = update_url_param(url, 'higherGrainValue', gcorId)
        self.sess.set.cookies(self.page.cookies())
        self.sess.get(new_url, retry=3)
        if 'issues' in self.sess.html:
            data_json = self.sess.json
            issues = data_json['issues']
            if not issues: logging(f'gcorId:{gcorId}的隐藏数据为空！')

            # pin.pin(f'开始记录')

            # for issue in tqdm(issues):
            #     sku = issue['grain']
            #     title = issue['title']
            #     asin = issue.get('itemInfo', {}).get('asin') or ''
            #     img = self.get_img_url_by_asin(asin)
            #     data_list.append({'sku': sku, 'title': title, 'asin': asin, 'img': img})

            def process_issue(issue):
                sku = issue['grain']
                title = issue['title']
                asin = issue.get('itemInfo', {}).get('asin') or ''
                img = self.get_img_url_by_asin(asin) if asin else ''
                return {'sku': sku, 'title': title, 'asin': asin, 'img': img}

            data_list.extend(multi_threaded_fetch_data(process_issue, issues))
            # pin.pin(f'{gcorId} {len(issues)}个数据执行时间')
        else:
            logging(f'gcorId:{gcorId}的隐藏数据获取失败！')
        return data_list

    def get_grain_compliance_requirement_issues(self, status_data='ACTIVE'):
        has_data = False
        url = f"{self.base_url}/spx/myc/myc-backend-service/api/get-grain-compliance-requirement-issues?sellingPartnerType=SELLER&grainTypes=SKU&grainTypes=ASIN&grainTypes=RPG&pageOffset=1&pageSize=1000&searchBy=ASIN&searchValue=&sortBy=GRAIN_GMS_60DAYS&sortDirection=DESCENDING&&grainProgramNames=PRODUCT_ASSURANCE&grainProgramNames=RLPSAS&grainProgramNames=HazardousGoodsSeller&grainProgramNames=EPR_DE_WEEE&grainProgramNames=HazardousGoods&grainProgramNames=eu_gpsr_markeplace_regulation&grainProgramNames=COSS_PRODUCT_SAFETY&grainProgramNames=COSS_FOOD_SAFETY&grainProgramNames=COSS_DIETARY_SUPPLEMENTS&grainRequirementIssueStatuses=RESOLVED&&&vendorCode="
        new_url = update_url_param(url, 'grainRequirementIssueStatuses', status_data)
        self.sess.set.cookies(self.page.cookies())
        self.sess.get(new_url, retry=3)
        if 'issues' in self.sess.html:
            data_json = self.sess.json
            issues = data_json['issues']
            if issues:
                has_data = True
        return has_data

    def get_cur_site_info(self):
        if 'chrome-extension' in self.page.url:
            self.page.get(self.home_url)
            if not self.check_page():
                return {}
        self.sess.set.cookies(self.page.cookies())
        url = f"{self.base_url}/account-switcher/regional-account/merchantMarketplace?mons_redirect=stck_reroute"
        self.sess.get(url)
        json_data = self.sess.json
        if json_data and json_data.get('regionalAccount'):
            regionalAccount = json_data.get('regionalAccount') or {}
            globalAccountId = regionalAccount.get('globalAccountId')
            label = regionalAccount.get('label')
            domain = regionalAccount.get('domain')
            ids = regionalAccount.get('ids') or {}
            mons_sel_dir_mcid = ids.get('mons_sel_dir_mcid')
            mons_sel_mkid = ids.get('mons_sel_mkid')
            mkid = mons_sel_mkid.split('.')[-1]
            if domain:
                site = domain.split('.')[-1]
            else:
                site = AmazonConfig.MARKETPLACETOCOUNTRY.get(mkid) or label
            href = f'{self.base_url}/home?mons_sel_dir_mcid={mons_sel_dir_mcid}&mons_sel_mkid={mons_sel_mkid}&mons_sel_dir_paid={mons_sel_dir_mcid}&ignore_selection_changed=true'
            self.cur_site_info = {
                'site': site,
                'label': label,
                'globalAccountId': globalAccountId,
                'mons_sel_dir_mcid': mons_sel_dir_mcid,
                'mons_sel_dir_paid': mons_sel_dir_mcid,
                'mons_sel_mkid': mons_sel_mkid,
                'href': href,
            }
            return self.cur_site_info

    def get_country_info(self, filter=True):
        if self.site_info:
            return self.site_info
        try:
            if 'chrome-extension' in self.page.url:
                self.page.get(self.home_url)
                if not self.check_page():
                    return {}
            self.sess.set.cookies(self.page.cookies())
            url = f"{self.base_url}/account-switcher/global-accounts"
            self.sess.get(url)
            json_data = self.sess.json
            if json_data and json_data.get('globalAccounts'):
                id = json_data.get('globalAccounts')[0].get('id')
                url = f"{self.base_url}/account-switcher/regional-accounts/merchantMarketplace?globalAccountId={id}"
                for _ in range(3):
                    self.sess.get(url)
                    result_json = self.sess.json
                    if result_json:
                        break
                else:
                    res = requests.get(url, cookies=self.page.cookies().as_dict())
                    result_json = res.json()
                regionalAccounts = result_json.get('regionalAccounts')
                for item in regionalAccounts:
                    mons_sel_dir_mcid = item.get('ids').get('mons_sel_dir_mcid')
                    mons_sel_mkid = item.get('ids').get('mons_sel_mkid')
                    mons_sel_dir_paid = item.get('globalAccountId')
                    label = item.get('label')
                    mkid = mons_sel_mkid.split('.')[-1]
                    domain = item.get('domain')
                    if domain:
                        site = domain.split('.')[-1]
                    else:
                        site = AmazonConfig.MARKETPLACETOCOUNTRY.get(mkid) or label
                    if filter and site not in set('UK、IT、DE、FR、ES'.lower().split('、')):
                        # logging(f'{site}非目标站点，跳过')
                        continue
                    href = f'{self.base_url}/home?mons_sel_dir_mcid={mons_sel_dir_mcid}&mons_sel_mkid={mons_sel_mkid}&mons_sel_dir_paid={mons_sel_dir_paid}&ignore_selection_changed=true'
                    self.country_info[site] = {
                        'site': site, 'label': label, 'mons_sel_dir_mcid': mons_sel_dir_mcid, 'mons_sel_mkid': mons_sel_mkid, 'mons_sel_dir_paid': mons_sel_dir_paid, 'href': href,
                    }
                    self.site_info[site] = href
        except Exception as e:
            traceback.print_exc()
            self.cur_info['result'] = f'|【{self.store_name}】获取站点切换链接异常: {e}'
            self.cur_info['finally_result'] += self.cur_info['result']
            self.cur_info['finally_status'] = 20
            logging(self.cur_info['result'])
            self.page.get_screenshot(get_err_path(), f'{self.store_name}_{now_str()}')
        return self.site_info

    def get_change_sites_url(self, home):
        try:
            self.page.get(home)
            if not self.check_page():
                return {}
            switcher = self.page('#sc-mkt-switcher-form') or self.page('.partner-switcher-container')
            if not switcher:
                self.page.refresh()
                self.page.wait.load_start()
                switcher = self.page('#sc-mkt-switcher-form') or self.page('.partner-switcher-container')
            switcher.click()
            # e = self.page('t=svg@@aria-hidden@@focusable', timeout=3)
            # if e and 'expanded' not in e.attrs['class']: e.click()
            merchants = self.page.eles('c=.merchant-level a', timeout=20)
            for merchant in merchants:
                href = merchant.attr('href')
                site = merchant.text
                if '待注册' in site or '(' in site:
                    continue
                self.site_info[site] = href
        except Exception as e:
            traceback.print_exc()
            self.cur_info['result'] = f'|【{self.store_name}】获取站点切换链接异常: {e}'
            self.cur_info['finally_result'] += self.cur_info['result']
            self.cur_info['finally_status'] = 20
            logging(self.cur_info['result'])
            self.page.get_screenshot(get_err_path(), f'{self.store_name}_{now_str()}')
        return self.site_info

    def download_top_search_terms(self):
        down_path = r'D:\rpa_download\excel\top-search-terms'
        self.page.set.download_path(down_path)
        self.page.set.when_download_file_exists('s')
        self.page.download.set.save_path(down_path)
        self.page.download.set.if_file_exists.skip()
        self.page.download.set.retry(3)

        # folders = check_fields_date(get_last_saturday(f="t"), down_path)
        folders = check_fields_date()

        if not folders:
            logging(f"本周报告已存在，无需重新下载!")

        def get_report_list(end_date):
            check_url = 'https://sellercentral.amazon.co.uk/api/brand-analytics/v1/dashboards/downloads'
            params = {
                "stck": "FE",
                "mons_redirect": "stck_reroute"
            }
            self.sess.set.cookies(self.page.cookies())
            self.sess.post(check_url, params=params)
            json_data = self.sess.json
            workflows = json_data.get('workflows', [])
            workflows = [w for w in workflows if w['reportEndDate'] == end_date]
            return workflows

        def down_file(record_locator, rename=None):
            param_url = "https://sellercentral.amazon.co.uk/api/brand-analytics/v1/report/download"
            params = {
                "stck": "FE",
                "mons_redirect": "stck_reroute"
            }
            data = {
                "recordLocator": record_locator
            }
            self.sess.set.cookies(self.page.cookies())
            self.sess.post(param_url, params=params, json=data)
            json_data = self.sess.json
            preSignedUrl = json_data.get('preSignedUrl', '')
            if preSignedUrl:
                mission = self.page.download.add(preSignedUrl, rename=rename)
                return mission
            return None

        self.page.get('https://sellercentral.amazon.co.uk/brand-analytics/dashboard/top-search-terms')
        countrys = self.page.eles('c=#countryPicker kat-option[value]').get.attrs('value')
        weekly = url_get_keyword(self.page.url, 'weekly-week')
        logging(f'当前周：{weekly}')
        report_list = get_report_list(weekly)
        exist_country = [item['countryCode'] for item in report_list]

        need_down_country = list(set(countrys) - set(exist_country))

        for country in need_down_country:
            url = f'https://sellercentral.amazon.co.uk/brand-analytics/dashboard/top-search-terms?country-id={country}'
            self.page.get(url)
            btn = self.page('#GenerateDownloadButton')
            btn.click()
            btn2 = self.page('#downloadModalGenerateDownloadButton')
            btn2.click('js')
            print(f'{country}已添加生成队列！')
            time.sleep(1)

        missions = []
        while True:
            report_list = get_report_list(weekly)
            for item in report_list:
                country = item['countryCode']
                recordLocator = item['recordLocator']
                status = item['status']
                if status == "SUCCESSFUL" and not any(m.data.rename == country for m in missions):
                    mission = down_file(recordLocator, country)
                    if mission: missions.append(mission)
            if len(missions) == len(countrys):
                logging(f'{weekly}所有报表已添加下载队列，共 {len(countrys)} 份文件，等待下载完成！')
                break
            logging(f'下载队列状态：{len(missions)}/{len(countrys)}')
            time.sleep(60)

        self.page.download.wait(show=True, timeout=None)
        failed_missions = self.page.download.get_failed_missions()
        if failed_missions:
            print(failed_missions)
            logging(f'下载失败，请重试，失败信息：{[i.file_name for i in failed_missions]}')
            return False
        logging(f'下载完毕,储存路径 {down_path}, 共 {len(countrys)} 个文件')
        excel_to_sql(down_path)
        return True

    def goods_diy(self, sku='', param_data=None, ):
        url_index = f"{self.base_url}/gestalt/managecustomization/index.html?sku={sku}"
        self.page.get(url_index)
        ka = self.page('c=kat-alert[variant="danger"]')
        if ka and ka.text.strip():
            self.cur_info['finally_result'] += f'|商品异常 {ka.text}！'
            logging('获取商品信息失败')
            self.cur_info['finally_status'] = 20
            self.get_cur_site_info()
            self.page.get_screenshot(get_err_path(), f'{self.store_name}_{self.cur_site_info["site"]}_{now_str()}')
            return False
        self.sess.set.cookies(self.page.cookies())

        # https://sellercentral.amazon.co.uk/gestalt/managecustomization/index.html?sku=YYX088-Kcaidzt-sxa03-GMDT
        # https://sellercentral.amazon.co.uk/gestalt/ajax/onebyone/init?sku=YYX088-Kcaidzt-sxa03-GMDT

        url_init = f"{self.base_url}/gestalt/ajax/onebyone/init?sku={sku}"
        self.sess.set.headers({
            "accept": "*/*",
            # "referer": f"{self.base_url}/gestalt/managecustomization/index.html",
        })
        self.sess.get(url_init)
        time.sleep(1)
        result_data = self.sess.json or {}
        if not result_data:
            self.cur_info['finally_result'] += f'|获取商品信息失败 {self.sess.html}！'
            logging('获取商品信息失败')
            self.cur_info['finally_status'] = 20
            return False
        if result_data.get('validationReport'):
            self.cur_info['finally_result'] += f'|{result_data["validationReport"].get("localizedErrorStrings")}'
            logging(self.cur_info['finally_result'])
            self.cur_info['finally_status'] = 20
            return False
        pendingVersionStatus = result_data.get("pendingVersionStatus") or result_data.get("status") or ''
        useCase = result_data.get("useCase")
        clientConfigVersion = result_data.get("clientConfigVersion")
        if pendingVersionStatus and 'InProgress' in pendingVersionStatus:
            self.cur_info['finally_result'] += f'|您最近所做的更改仍在处理中,最多可能需要 30 分钟将更改发布到亚马逊。 发布完成后，您将能够进行其他更改。！'
            logging(self.cur_info['finally_result'])
            self.cur_info['finally_status'] = 20
            return False
        logging('可以修改')
        GoogleFonts = [i.get('fontFamily') for i in (result_data.get('fonts') or []) if i.get('fontVariants')]
        try:
            clientConfig = json.loads(result_data.get("clientConfig"))
            template_identifier_map = get_template_identifier(clientConfig)
            bodyBuilder = GoodDiyRequestBodyBuilder()
            bodyBuilder.template_identifier_map = template_identifier_map
            if GoogleFonts: bodyBuilder.GoogleFonts = GoogleFonts
            param_data = bodyBuilder.reconstruct_data(param_data)
            if bodyBuilder.err:
                self.cur_info['finally_result'] += bodyBuilder.err
                self.cur_info['finally_status'] = 20
        except Exception as e:
            self.cur_info['finally_result'] += f'|参数解析异常：{e}'
            logging(self.cur_info['finally_result'])
            self.cur_info['finally_status'] = 20
            return False

        param_data = self.upload_img(param_data)
        self.sess.set.headers({
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "upgrade-insecure-requests": "1",
        })
        self.sess.get(url_index)
        time.sleep(1)
        csrftoken = self.sess.response.headers.get("anti-csrftoken-a2z")

        url = f"{self.base_url}/gestalt/ajax/onebyone/setSellerSpec"
        params = {
            "sku": sku,
            "useCase": useCase,
            "clientConfigVersion": clientConfigVersion,
            "listingType": "EDIT"
        }
        self.sess.set.headers({
            "accept": "*/*",
            "accept-language": "zh-TW,zh;q=0.9",
            "content-type": "application/json",
            "anti-csrftoken-a2z": csrftoken,
            "origin": f"{self.base_url}",
            # "referer": f"{self.base_url}/gestalt/managecustomization/index.html",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "x-requested-with": "XMLHttpRequest"
        })
        self.sess.post(url, params=params, json=param_data)
        time.sleep(1)
        if self.sess.json and self.sess.json.get('identifier'):
            self.cur_info['finally_result'] += f'|商品定制已发布'
            logging(self.cur_info['finally_result'])
            # self.page.get(url_index)
            # pg.alert('商品定制已发布，请检查效果')
            return True
        elif not self.sess.json:
            self.cur_info['finally_result'] += f'|商品定制发布异常，请检查参数！{self.sess.html}'
            logging(self.cur_info['finally_result'])
            self.cur_info['finally_status'] = 20
            return False
        else:
            self.cur_info['finally_result'] += f'|商品定制发布失败！{self.sess.json}'
            logging(self.cur_info['finally_result'])
            self.cur_info['finally_status'] = 20
            return False

    def upload_img(self, data):
        '''
        递归处理data，如果值是dict切同时拥有imageUrl，和dimension则取imageUrl的值做操作后，取返回值，替换imageUrl和dimension.width 和 dimension.height
        :param data:
        :return:
        '''
        if isinstance(data, dict):
            if 'imageUrl' in data and 'dimension' in data and isinstance(data['dimension'], dict):
                image_url = data['imageUrl']

                image_content, resolution, hash, suffix = load_img_info(image_url)  # 获取图片信息
                data['dimension']['width'] = resolution[0]
                data['dimension']['height'] = resolution[1]
                new_img_url = self.upload_img_to_server(image_content, hash, suffix, image_url)  # 上传图片，返回新的图片url
                # 替换 imageUrl 和 dimension
                data['imageUrl'] = new_img_url
                # logging(f'源{image_url}已更换为{new_img_url}\n尺寸{width}x{height} 替换为{resolution[0]}x{resolution[1]}')
            # 递归处理字典中的每个值
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    self.upload_img(value)
        elif isinstance(data, list):
            for item in data:
                self.upload_img(item)
        return data

    def upload_img_to_server(self, image_content, hash, suffix, cur_img_url):
        temp_sess = self.sess
        temp_sess.set.cookies(self.page.cookies())
        temp_sess.set.headers({
            "accept": "*/*",
            "accept-language": "zh-TW,zh;q=0.9",
            # "referer": f"{self.base_url}/gestalt/managecustomization/index.html",
            "x-requested-with": "XMLHttpRequest"
        })

        url = f"{self.base_url}/gestalt/ajax/imageUpload/policy"
        params = {
            "extension": f'.{suffix}',
            "type": "SellerConfig",
            "hash": hash
        }
        temp_sess.get(url, params=params)
        json_data = temp_sess.json

        if json_data:
            # 使用解包语法简化赋值
            algorithm, bucket, credentials, date, encodedPolicy, key, signature = [
                json_data.get(key.strip()) for key in
                'algorithm, bucket, credentials, date, encodedPolicy, key, signature'.split(',')
            ]
            content_type = f'image/{"jpeg" if suffix == "jpg" else suffix}'

            files = {
                'key': key,
                'Content-Type': content_type,
                'x-amz-credential': credentials,
                'x-amz-algorithm': algorithm,
                'x-amz-date': date,
                'success_action_status': 200,
                'policy': encodedPolicy,
                'x-amz-signature': signature,
                'file': image_content,
            }
            headers = {
                'Accept': '*/*',
                'Accept-Language': 'zh-TW,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'close',
                'Origin': f'{self.base_url}',
                'Pragma': 'no-cache',
                # 'Referer': f'{self.base_url}/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            }
            temp_sess.set.headers(headers)

            url = f"https://{bucket}.s3.amazonaws.com/"
            try:
                for _ in range(3):
                    rs = temp_sess.session.post(url, files=files, timeout=120)
                    if rs.status_code == 200:
                        img_url = f'https://m.media-amazon.com/images/S/{bucket}/{key}'
                        time.sleep(1)
                        # print(f'上传成功 图片url:{img_url}')
                        return img_url
                else:
                    raise Exception(f'上传失败超过最大重试次数！')
            except Exception as e:
                traceback.print_exc()
                raise Exception(f'图片{cur_img_url}上传亚马逊异常:{str(e)}')

    def download_payment_reports(self):
        """下载店铺各站点的付款报告PDF"""
        # 设置下载路径
        down_path = pdf_temp_download_path
        self.page.set.download_path(down_path)
        self.page.set.when_download_file_exists('overwrite')
        self.page.download.set.save_path(down_path)
        self.page.download.set.if_file_exists.overwrite()
        self.page.download.set.retry(3)

        # 获取所有站点信息
        for retry in range(3):
            try:
                self.get_country_info(False)
                if self.country_info:
                    break
                time.sleep(1)
            except Exception as e:
                logging(f'获取站点信息第{retry + 1}次失败: {e}')
                if retry == 2:
                    logging('获取站点切换链接失败！')
                    return False
                time.sleep(2)

        missions = []
        tab_switch = self.page.new_tab(background=True)
        task_site_name_list = self.cur_info.get('switch_data', {}).get(self.store_name, [])

        # 遍历所有站点
        for site in task_site_name_list:
            for retry_site in range(3):
                try:
                    site_cn = site_map.get(site, site)
                    site_name = self.country_info.get(site, {}).get('label') or self.country_info.get(site_cn, {}).get('label')
                    if site != site_name and site_cn != site_name:
                        raise Exception(f'{store_name} {site} 站点切换失败！')

                    logging(f"{self.store_name} 切换站点：{site}-{site_cn} - {site_name}")
                    site_url = self.country_info.get(site, {}).get('href') or self.country_info.get(site_cn, {}).get('href')

                    # 切换到当前站点
                    tab_switch.get(site_url)
                    if not self.check_page(tab_switch):
                        raise Exception('页面检查失败')

                    # 进入报告页面
                    report_url = f'{self.base_url}/payments/reports-repository/ref=xx_rrepo_dnav_xx'
                    self.page.get(report_url)

                    if not self._check_and_download_report(missions, site):
                        # 处理每个月份的下载
                        for year_month in [('2025', '2')]:
                            year, month = year_month
                            for retry_month in range(3):
                                try:
                                    # 选择汇总类型为"汇总"
                                    self._select_summary_type()

                                    # 选择年月
                                    self._select_year_month(year, month)

                                    # 点击请求报告
                                    self.page('#filter-generate-button').click()
                                    time.sleep(1)

                                    break  # 成功后跳出重试循环
                                except Exception as e:
                                    logging(f'处理{year}年{month}月报告第{retry_month + 1}次失败: {e}')
                                    if retry_month == 2:
                                        self.cur_info['fail_data'].append(f"{self.store_name}-{site}-{year}-{month}")
                                    self.page.refresh()
                                    time.sleep(2)

                        # 等待报告生成并下载
                        if not self._wait_and_download_report(missions, site):
                            raise Exception(f'报告生成或下载失败')
                    break  # 成功后跳出站点重试循环
                except Exception as e:
                    traceback.print_exc()
                    logging(f'处理站点{site}第{retry_site + 1}次失败: {e}')
                    if retry_site == 2:
                        self.cur_info['fail_data'].append(f"{self.store_name}-{site}")
                    time.sleep(2)

        # 等待所有下载完成
        if missions:
            self.page.download.wait()
            failed_missions = self.page.download.get_failed_missions()
            if failed_missions:
                failed_files = [m.file_name for m in failed_missions]
                logging(f'下载失败的任务：{failed_files}')
                self.cur_info['fail_data'].extend(failed_files)
                return False
            logging(f'所有报告下载完成，存储路径：{down_path},本次')
            return True
        logging(f'所有报告下载完成，存储路径：{down_path}')
        return False

    def download_invoice(self):
        """下载店铺各站点的付款报告PDF"""
        # 设置下载路径
        down_path = invoice_download_path
        self.page.set.download_path(down_path)
        self.page.set.when_download_file_exists('s')
        self.page.download.set.save_path(down_path)
        self.page.download.set.if_file_exists.skip()
        self.page.download.set.retry(5)
        self.page.download.set.timeout(600)

        missions = []
        try:
            # 进入报告页面
            report_url = f'{self.ads_base_url}/ads-bg/billing/history?invoiceTab=%2522paid%2522'
            self.page.get(report_url)
            self.check_page()
            e_script = self.page('csrfToken')
            context = e_script.inner_html
            accountid = re.findall(r'globalAccountId: "(.*)",', context)[0]
            advertiserid = re.findall(r'advertiserId: "(.*)",', context)[0]
            token = re.findall(r"csrfToken: '(.*)',", context)[0]
            clientid = re.findall(r'clientId: "(.*)",', context)[0]
            marketplaceid = re.findall(r'marketplaceId: "(.*)",', context)[0]

            temp_sess = self.sess
            temp_sess.set.cookies(self.page.cookies())
            temp_sess.set.headers({
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "id",
                "Amazon-Ads-AccountId": accountid,
                "Amazon-Advertising-API-AdvertiserId": advertiserid,
                "Amazon-Advertising-API-CSRF-Data": clientid,
                "Amazon-Advertising-API-CSRF-Token": token,
                "Amazon-Advertising-API-ClientId": clientid,
                "Amazon-Advertising-API-MarketplaceId": marketplaceid,
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "application/json",
                "Prefer": "return=representation",
                "X-Amz-Request-Csrf-Token": "true",
                "anti-csrftoken-a2z": token,
            })
            url_invice_list = f"{self.ads_base_url}/a9g-api-gateway/invoiceSummaries/list"
            data = {
                "maxResults": 200,
                "invoiceDueDateRangeFilter": {
                    "startDate": self.download_invoice_date_range.get('start_date'),
                    "endDate": self.download_invoice_date_range.get('end_date')
                },
                "countryCodeFilter": {
                    "include": []
                },
                "statusValueFilter": {
                    "include": [
                        "Written Off",
                        "Paid In Full"
                    ],
                    "queryTermMatchType": "EXACT_MATCH"
                },
                "paymentMethodTypeValueFilter": {
                    "include": [],
                    "queryTermMatchType": "EXACT_MATCH"
                },
                "invoiceNumberFilter": {
                    "include": []
                },
                "reserveOrderNumberFilter": {
                    "include": []
                }
            }
            nextToken = ''
            while True:
                temp_sess.post(url_invice_list, json=data)
                json_data = temp_sess.json or {}
                data_list = json_data.get('invoiceSummaries', [])

                if data_list:
                    item_data = {}
                    for data in data_list:
                        documentId = data.get('id')
                        invoiceIssuedDate = data.get('invoiceIssuedDate')
                        # 命名格式”店铺名称&日期&发票号码“
                        fileName = f'{self.store_name}&{documentId}&{invoiceIssuedDate}'
                        item_data[documentId] = invoiceIssuedDate

                        gen_url = f'{self.ads_base_url}/a9g-api-gateway/billing/documents/{documentId}?docType=INVOICE&docType=CREDIT_MEMO&docType=GIS_INVOICE&docType=GIS_CREDIT_MEMO&docType=PAYMENT_COMPLEMENT'
                        for i in range(20):
                            temp_sess.get(gen_url)
                            cur_document_info = temp_sess.json or {}
                            if not cur_document_info.get('availableDocuments'):
                                continue
                            storagePath = cur_document_info['availableDocuments'][0]['storagePath']
                            break
                        else:
                            self.cur_info['fail_data'].append(fileName)
                            logging(f'下载文件失败：{fileName}')
                            continue
                        mission = self.page.download.add(file_url=storagePath, rename=fileName)
                        missions.append(mission)
                        logging(f'【{self.store_name}】{fileName} 已添加下载队列')
                    '''
                    url_exports = f'{self.ads_base_url}/a9g-api-gateway/billing/documents/exports'
                    params = {
                        "documentCollections": [{"docTypes": ["INVOICE"], "documentId": id} for id in list(item_data.keys())],
                        "format": "JSON"
                    }
                    temp_sess.post(url_exports, json=params)  # exports
                    exportId = temp_sess.json['exportId']

                    url_exports_info = f'{url_exports}/{exportId}'
                    for _ in range(5):
                        temp_sess.get(url_exports_info)
                        document_info = temp_sess.json or {}
                        if document_info.get('documentCollections'):
                            break
                        time.sleep(3)
                    else:
                        raise Exception('获取发票导出url相关信息失败！')
                    for file in document_info['documentCollections']:
                        documentId = file['documentId']
                        # 命名格式”店铺名称&日期&发票号码“
                        fileName = f'{self.store_name}&{documentId}&{item_data.get(documentId, "异常")}'
                        availableDocuments = file['availableDocuments']
                        if availableDocuments:
                            storagePath = availableDocuments[0]['storagePath']
                        else:
                            gen_url = f'{self.ads_base_url}/a9g-api-gateway/billing/documents/{documentId}?docType=INVOICE&docType=CREDIT_MEMO&docType=GIS_INVOICE&docType=GIS_CREDIT_MEMO&docType=PAYMENT_COMPLEMENT'
                            for i in range(20):
                                temp_sess.get(gen_url)
                                cur_document_info = temp_sess.json or {}
                                if not cur_document_info.get('availableDocuments'):
                                    continue
                                storagePath = cur_document_info['availableDocuments'][0]['storagePath']
                                break
                            else:
                                self.cur_info['fail_data'].append(fileName)
                                logging(f'下载文件失败：{fileName}')
                        mission = self.page.download.add(file_url=storagePath, rename=fileName)
                        missions.append(mission)
                        '''
                if len(data_list) < 200:
                    break
                Token = json_data.get('nextToken')
                if Token == nextToken:
                    break
                nextToken = Token
                data['nextToken'] = nextToken
        except Exception as e:
            traceback.print_exc()
            logging(f'店铺{self.store_name}存在下载失败发票: {e}')
            self.cur_info['fail_data'].append(f"{self.store_name}")
            time.sleep(2)

        # 等待所有下载完成
        if missions:
            self.page.download.wait()
            failed_missions = self.page.download.get_failed_missions()
            if failed_missions:
                failed_files = [m.file_name for m in failed_missions]
                logging(f'【{self.store_name}】下载失败的任务：{failed_files}')
                self.cur_info['fail_data'].extend(failed_files)
            logging(f'【{self.store_name}】所有报告下载完成，本次数量为：{len(missions) - len(failed_missions)}，存储路径：{down_path}')
            return True
        logging(f'{self.store_name}执行完毕')
        return False

    def _select_summary_type(self):
        """选择汇总类型为"汇总" """
        max_retries = 3
        for retry in range(max_retries):
            try:
                if 'title="汇总"' in self.page('c=.report-type-selection kat-dropdown').sr.html:
                    break
                ele_select = self.page('c=.report-type-selection kat-dropdown')
                ele_select.click()
                time.sleep(1)
                ele_hz = ele_select.sr('c=[value="SELLER_SUMMARY_DATE_RANGE"]')
                if ele_hz:
                    ele_hz.click(by_js=None)
                time.sleep(1)
                break
            except Exception as e:
                if retry == max_retries - 1:
                    raise Exception(f'选择汇总类型失败: {e}')
                self.page.refresh()
                time.sleep(2)

    def _select_year_month(self, year, month):
        """选择年月"""
        try:
            self.page('c=[aria-label="月"]').click()
            time.sleep(1)

            ele_year = self.page('c=.dateRange-type-selection kat-dropdown', 2)
            ele_year.click()
            time.sleep(1)
            ele_year.sr(f'c=[value="{year}"]').click()
            time.sleep(1)

            ele_month = self.page('c=.dateRange-type-selection kat-dropdown', 1)
            ele_month.click()
            time.sleep(1)
            ele_month.sr(f'c=[value="{int(month) - 1}"]').click()
            time.sleep(1)
        except Exception as e:
            raise Exception(f'选择年月失败: {e}')

    def _check_and_download_report(self, missions, site):
        """检测报告并下载"""
        try:
            eles_row = self.page.eles('c=kat-table-body kat-table-row')[:1]
            status_2025_2 = False
            for ele_row in eles_row:
                req_date = ele_row('.header-cell-request-date').text
                report_start_date = ele_row('.header-cell-start-date').text
                report_type = ele_row('.header-cell-report-type').text
                report_tag = ele_row('.header-cell-report-tag').text
                report_status = ele_row('c=.header-cell-report-status kat-statusindicator').attr('label')
                report_action = ele_row('c=.header-cell-report-action kat-button')
                report_action_label = report_action.attr('label')
                if (
                        any(x in req_date for x in ('05'))
                        and report_type == '汇总'
                        and report_tag == '日期范围汇总报告'
                        and report_status == '准备就绪'
                        and '下载 PDF 文件' == report_action_label
                ):
                    if '2025' in report_start_date and any(x in report_start_date for x in {'Feb', 'February', '2月'}):
                        file_name = f"{self.store_name}&{site}&2025-2"
                        status_2025_2 = True
                    else:
                        # pg.alert(f'报告日期异常: {report_start_date}')
                        raise Exception(f'报告日期异常: {report_start_date}')
                    mission = report_action.click.to_download(save_path=pdf_temp_download_path, rename=file_name, new_tab=True)
                    mission.wait()
                    if mission:
                        logging(f"添加下载任务：{file_name}")
                        missions.append(mission)
            if not status_2025_2:
                # pg.alert(f'{site} 报告生成异常')
                raise Exception(f'{site} 报告生成异常')
            return True
        except Exception as e:
            traceback.print_exc()
            logging(f'等待报告生成时发生错误: {e}')
            time.sleep(2)

    def _wait_and_download_report(self, missions, site):
        """等待报告生成并下载"""
        down_path = pdf_temp_download_path
        start_time = time.time()
        timeout = 180

        while time.time() - start_time < timeout:
            try:
                eles_action = self.page.eles('c=.header-cell-report-action kat-button')[:1]
                labels = eles_action.get.attrs('label')

                if all(ele == '下载 PDF 文件' for ele in labels):
                    # if eles_action[0].attr('label') == '下载 PDF 文件' and eles_action[1].attr('label') == '下载 PDF 文件':
                    for action in eles_action:
                        startDate = action.parent('c=[role="row"] .header-cell-start-date').text
                        if '汇总' not in action.parent('c=[role="row"] .header-cell-report-type').text:
                            raise Exception(f'报告类型不是汇总')

                        if '2025' in startDate:
                            file_name = f"{self.store_name}&{site}&2025-2"
                        else:
                            raise Exception(f'报告日期异常: {startDate}')

                        mission = action.click.to_download(save_path=down_path, rename=file_name)
                        mission.wait()
                        if mission:
                            missions.append(mission)
                            logging(f"添加下载任务：{file_name}")
                    return True
                else:
                    for action in eles_action[:2]:
                        if action.attr('label') == '刷新':
                            action.click()
                        elif '下载 PDF 文件' in action.attr('label'):
                            continue
                        if all(x not in action.attr('label') for x in ['pdf', '刷新']):
                            # print(f'自动化操作异常，日期没选对！请手动处理！')
                            # pg.alert(f'自动化操作异常，日期没选对！请手动处理！')
                            pass
                    time.sleep(3)
                    self.page.refresh()
                #
                # for action in eles_action[:2]:
                #     # if action.attr('label') == '刷新':
                #     #     action.click()
                #     if '修改' in action.attr('label'):
                #         pg.alert(f'{store_name}自动化操作异常，日期没选对！请手动处理！')
                # elif '下载 PDF 文件' in action.attr('label'):
                #     continue
                # else:
                #     raise Exception(f'按钮当前文字: {action.attr("label")} 操作异常')
                time.sleep(3)

            except Exception as e:
                traceback.print_exc()
                logging(f'等待报告生成时发生错误: {e}')
                time.sleep(2)

        return False


class StoreManager(SuperBrowserClient):
    def __init__(self, socket_port=None, user_info=None, exe_path='', max_workers: int = 7):
        super().__init__(exe_path, socket_port)
        self.max_workers = max_workers
        self.user_info = user_info or {
            "company": "KUIMINGKEJI",
            "username": "yxy669",
            "password": "yxy669@@",
            # "username": "yxyjs02",
            # "password": "yxyjs02..",
        }
        self.task_params = {
            'fail_data': [],
            'task_datas': [],
            'result': '',
            'finally_result': '',
            'finally_status': 10,
            'platform_num': 0,
            'task_num': 0,
        }

    def send_http(self, data):
        """发送HTTP请求到紫鸟客户端。"""

        err_msg = ''
        for _ in range(5):
            try:
                url = f'http://127.0.0.1:{self.socket_port}'
                response = requests.post(url, json.dumps(data).encode('utf-8'), timeout=120)
                return response.json()
            except (ConnectionError, NewConnectionError) as e:
                self.socket_port = self.reboot()
                err_msg = f'第{_ + 1}次失败，切换端口为{self.socket_port}。{e}'
                logging(err_msg)
            except Exception as e:
                err_msg = f'第{_ + 1}次失败.{e}'
        self.task_params['finally_result'] += f'|{err_msg}'
        self.task_params['finally_status'] = 20
        print(err_msg)
        fsmsg.send(None, f'{data.get("action", "")} 失败！{err_msg}')
        return {}

    def handle_response(self, response):
        """处理来自浏览器客户端的响应。"""
        err_msg = ''
        try:
            status_code = response.get("statusCode", '')
            if str(status_code) == "0":
                return response
            elif str(status_code) == "-10003":
                err_msg = f"|登录错误: {json.dumps(response, ensure_ascii=False)}"
            elif not response:
                err_msg = f"|请求失败！"
            else:
                err_msg = f"|失败: {json.dumps(response, ensure_ascii=False)}"
        except Exception as e:
            err_msg = e
            err_msg = f"|处理响应时发生错误: {err_msg}"
        print(err_msg)
        self.task_params['finally_result'] += err_msg
        self.task_params['finally_status'] = 20
        return False

    def open_store(self, store_info, **kwargs):
        request_id = str(uuid.uuid4())
        data = {
            "action": "startBrowser",
            # "action": "getBrowserEnvInfo",
            "isWaitPluginUpdate": 0,
            "isHeadless": kwargs.get('isHeadless', 0),
            "requestId": request_id,
            "isWebDriverReadOnlyMode": kwargs.get('isWebDriverReadOnlyMode', 0),
            "cookieTypeLoad": 0,
            "cookieTypeSave": kwargs.get('cookieTypeSave', 0),
            "runMode": "1",
            "isLoadUserPlugin": False,
            "pluginIdType": 1,
            "privacyMode": kwargs.get('isprivacy', 0)
        }
        data.update(self.user_info)
        data.update({"browserId" if store_info.isdigit() else "browserOauth": store_info})
        jsInfo = kwargs.get('jsInfo', '')
        if len(str(kwargs.get('jsInfo', ''))) > 2:
            data["injectJsInfo"] = json.dumps(jsInfo)
        return self.handle_response(self.send_http(data))

    def close_store(self, browser_oauth):
        """关闭浏览器中的商店。"""
        request_id = str(uuid.uuid4())
        data = {
            "action": "stopBrowser",
            "requestId": request_id,
            "duplicate": 0,
            "browserOauth": browser_oauth
        }
        data.update(self.user_info)
        self.task_params['cur_store_info'] = None
        return self.handle_response(self.send_http(data))

    def get_browser_list(self):
        """获取可用浏览器的列表。"""
        print("=====获取商店列表=====")
        request_id = str(uuid.uuid4())
        data = {
            "action": "getBrowserList",
            "requestId": request_id
        }
        data.update(self.user_info)

        response = self.send_http(data)
        if response and self.handle_response(response):
            print(response.get("action"), len(response.get("browserList")), '个商店')
            # print(response.get("browserList"))
            return response.get("browserList")
        else:
            return None

    def get_exit(self):
        """ 关闭客户端 """
        data = {"action": "exit", "requestId": str(uuid.uuid4())}
        data.update(self.user_info)
        print('@@ get_exit...' + json.dumps(data, ensure_ascii=False))
        self.send_http(data)

    def use_one_browser_run_task(self, store_info):
        """
        打开一个店铺运行脚本
        :param store_info: 店铺信息
        """
        # 如果要指定店铺ID, 获取方法:登录紫鸟客户端->账号管理->选择对应的店铺账号->点击"查看账号"进入账号详情页->账号名称后面的ID即为店铺ID
        result_datas = []
        store_id = store_info.get('browserOauth')
        store_name = store_info.get("browserName")
        print(f"=====打开商店：{store_name}=====")

        ret_json = self.open_store(store_id)
        if not ret_json:
            self.task_params['finally_result'] += f'|【{store_name}】 打开失败！'
            self.task_params['finally_status'] = 20
            return
        store_start_info = {**store_info, **ret_json}
        ziniao = ZiNiaoDP(store_start_info)

        if ziniao.page is None:
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        ip_check_url = store_start_info.get("ipDetectionPage")
        if not ip_check_url:
            print("IP检测页地址为空，请升级紫鸟浏览器到最新版")
            ziniao.page.quit()
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return result_datas

        try:
            if ziniao.open_ip_check(ip_check_url):
                print("IP检测通过，打开商店平台主页")
                # 主页切换站点
                # 'https://www.amazon.de'
                if 'sellercentral' in ziniao.home_url:
                    # site_info = ziniao.get_change_sites_url(ziniao.home_url)
                    site_info = ziniao.get_country_info()
                    if not site_info:
                        logging('获取站点切换链接失败！')
                    for site, site_url in site_info.items():
                        # if is_test_environment():
                        #     if site_name != '西班牙': continue
                        site_name = ziniao.country_info.get(site, {}).get('label')
                        logging(f"{store_name} 切换站点：{site} - {site_name}")
                        ziniao.page.get(site_url)
                        ziniao.cur_info['siteCN'] = site_name
                        ziniao.cur_info['site'] = site
                        if not ziniao.check_page():
                            continue
                        # ziniao.page.get('https://sellercentral.amazon.co.uk/spx/myc/dashboard')
                        results = ziniao.get_compliance_information() or []
                        result_datas.extend(results)
                    print(f"==============================商店：{store_name} 合规信息数量：{len(result_datas)}==============================")
                else:
                    logging('非后台店铺！')
        except Exception as e:
            self.task_params['finally_result'] += f'【{store_name}】脚本运行异常:|{e}'
            ziniao.page.get_screenshot(get_err_path(), f'【{store_name}】_{now_str()}')
            print(f"【{store_name}】脚本运行异常:" + traceback.format_exc())
            self.task_params['finally_status'] = 20
            ziniao.cur_info['status'] = 20
        finally:
            # if ziniao.cur_info.get("status") == 10 and pymsgbox.confirm('是否关闭浏览器？') == 'OK':
            finally_status = max(ziniao.cur_info.get("status", 20), ziniao.cur_info.get("finally_status", 20))
            if finally_status == 20: self.task_params['finally_result'] += ziniao.cur_info.get("finally_result")
            self.task_params['finally_status'] = max(ziniao.cur_info.get("finally_status", 20), self.task_params['finally_status'])
            self.task_params['task_datas'].extend(result_datas)

            ziniao.page.quit()
            print(f"=====脚本执行结束，关闭商店：{store_name}=====")
            self.close_store(store_id)

            self.task_params['platform_num'] += ziniao.cur_info.get("total_items") or 0
            self.task_params['task_num'] += len(result_datas)
            insert_or_update_amazon_rule_datas(result_datas, self.task_params) if result_datas else None

            return result_datas

    def task_download_top_search_terms(self, store_info):
        """
        打开一个店铺运行脚本
        :param store_info: 店铺信息
        """
        # 如果要指定店铺ID, 获取方法:登录紫鸟客户端->账号管理->选择对应的店铺账号->点击"查看账号"进入账号详情页->账号名称后面的ID即为店铺ID
        store_id = store_info.get('browserOauth')
        store_name = store_info.get("browserName")
        print(f"=====打开商店：{store_name}=====")

        ret_json = self.open_store(store_id)
        if not ret_json:
            self.task_params['finally_result'] += f'|【{store_name}】 打开失败！'
            self.task_params['finally_status'] = 20
            return
        store_start_info = {**store_info, **ret_json}
        ziniao = ZiNiaoDP(store_start_info)

        if ziniao.page is None:
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        ip_check_url = store_start_info.get("ipDetectionPage")
        if not ip_check_url:
            print("IP检测页地址为空，请升级紫鸟浏览器到最新版")
            ziniao.page.quit()
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return ''

        try:
            if ziniao.open_ip_check(ip_check_url):
                print("IP检测通过，打开商店平台主页")
                ziniao.download_top_search_terms()
                print(f"==============================商店：{store_name} ==============================")
        except Exception as e:
            self.task_params['finally_result'] += f'【{store_name}】脚本运行异常:|{e}'
            ziniao.page.get_screenshot(get_err_path(), f'【{store_name}】_{now_str()}')
            print(f"【{store_name}】脚本运行异常:" + traceback.format_exc())
            self.task_params['finally_status'] = 20
            ziniao.cur_info['status'] = 20
        finally:
            finally_status = max(ziniao.cur_info.get("status", 20), ziniao.cur_info.get("finally_status", 20))
            if finally_status == 20: self.task_params['finally_result'] += ziniao.cur_info.get("finally_result")
            self.task_params['finally_status'] = max(ziniao.cur_info.get("finally_status", 20), self.task_params['finally_status'])

            ziniao.page.quit()
            print(f"=====脚本执行结束，关闭商店：{store_name}=====")
            self.close_store(store_id)

            self.task_params['platform_num'] += ziniao.cur_info.get("total_items") or 0

            # MS = MysqlHelper()
            rs = MS.get_one(f'SELECT count(1) FROM rpa.`data_amazon_top_search_terms` where datetime >= {get_last_saturday(f="i")}')
            data_count = rs[0]
            weekly = get_last_saturday(f="s")
            fsmsg.send('ABA数据采集提醒', f'本周ABA数据采集完毕，报告日期为{weekly}，已更新{data_count}条数据')

            return True

    def task_goods_diy(self, store_info):
        """
        打开一个店铺运行脚本
        :param store_info: 店铺信息
        """
        # 如果要指定店铺ID, 获取方法:登录紫鸟客户端->账号管理->选择对应的店铺账号->点击"查看账号"进入账号详情页->账号名称后面的ID即为店铺ID
        first_store = False

        if not self.task_params.get('cur_store_info'):
            store_id = store_info.get('browserOauth')
            store_name = store_info.get("browserName")
            print(f"=====打开商店：{store_name}=====")

            ret_json = self.open_store(store_id)
            if not ret_json:
                self.task_params['finally_result'] += f'|【{store_name}】 打开失败！'
                self.task_params['finally_status'] = 20
                return
            store_start_info = {**store_info, **ret_json}
            self.task_params['cur_store_info'] = store_start_info
            first_store = True
        ziniao = ZiNiaoDP(self.task_params['cur_store_info'])
        store_name = self.task_params.get('cur_store_info', {}).get('browserName')
        store_id = self.task_params.get('cur_store_info', {}).get('browserOauth')

        if ziniao.page is None:
            self.task_params['finally_result'] += f'店铺连接失败！'
            self.task_params['finally_status'] = 20
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        if first_store:
            ip_check_url = self.task_params.get('cur_store_info', {}).get("ipDetectionPage")
            if not ip_check_url or not ziniao.open_ip_check(ip_check_url):
                print("IP检测页地址为空，请升级紫鸟浏览器到最新版")
                ziniao.page.quit()
                print(f"=====关闭商店：{store_name}=====")
                self.close_store(store_id)
                return ''
            print("IP检测通过，打开商店平台主页")
            if 'chrome-extension' in ziniao.page.url:
                ziniao.page.get(ziniao.home_url)
                ziniao.check_page()

        try:
            platform_site = self.task_params.get('platform_site', '').lower()
            seller_sku = self.task_params.get('seller_sku')

            param_data = json.loads(self.task_params.get('task_content', '{}'))

            if not self.task_params.get('cur_site_info'):
                self.task_params['cur_site_info'] = ziniao.get_country_info(False)
            site_info = self.task_params.get('cur_site_info', {})
            task_site = site_info.get(platform_site)
            print(f'目标站点：{platform_site}。即将切换站点:{task_site}')
            if site_info and task_site:
                cur_site = ''
                for _ in range(3):
                    cur_site_info = ziniao.get_cur_site_info()
                    if not cur_site_info:
                        continue
                    cur_site = cur_site_info.get('site', '')
                    if self.task_params.get('cur_site_code') != platform_site or 'selection?' in ziniao.page.url or cur_site != platform_site:
                        self.task_params['cur_site_info'] = ziniao.get_country_info(False)
                        site_info = self.task_params.get('cur_site_info', {})
                        task_site = site_info.get(platform_site)
                        ziniao.page.get(task_site)
                        if not ziniao.check_page():
                            continue
                        if cur_site != platform_site:
                            continue
                    break
                else:
                    err = ziniao.cur_info['finally_result']
                    self.task_params['cur_site_info'] = {}
                    raise Exception(f'|切换站点失败,目标站点：{platform_site}，当前站点：{cur_site}。 {err}')
                ziniao.goods_diy(sku=seller_sku, param_data=param_data)
                self.task_params['cur_site_code'] = platform_site
            print(f"==============================商店：{store_name} ==============================")
        except Exception as e:
            self.task_params['finally_result'] += f'【{store_name}】脚本运行异常:|{e}'
            ziniao.page.get_screenshot(get_err_path(), f'【{store_name}】_{now_str()}')
            self.task_params['finally_status'] = 20
            ziniao.cur_info['status'] = 20
            print(f"【{store_name}】脚本运行异常:" + traceback.format_exc())
            # fsmsg.send(None, None, traceback.format_exc())
        finally:
            finally_status = max(ziniao.cur_info.get("status", 20), ziniao.cur_info.get("finally_status", 20))
            if finally_status == 20:
                self.task_params['finally_result'] += ziniao.cur_info.get("finally_result")
            self.task_params['finally_status'] = max(ziniao.cur_info.get("finally_status", 20), self.task_params['finally_status'])

            if MS.get_one(f'select count(1) from bms.amazon_task_diy where super_browser_name = "{store_name}" and status =1')[0] == 0:
                print(f"=====脚本执行结束，关闭商店：{store_name}=====")
                self.task_params['cur_site_info'] = {}
                ziniao.page.quit()
                self.close_store(store_id)
            return True

    def use_all_browser_run_task(self, browser_list):
        """使用多线程循环打开所有商店运行脚本，最大线程数为7。"""
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.use_one_browser_run_task, browser) for browser in browser_list}
            concurrent.futures.wait(futures)

    def task_download_payment_reports(self, store_info):
        """
        为指定店铺下载付款报告
        :param store_info: 店铺信息
        """
        store_id = store_info.get('browserOauth')
        store_name = store_info.get("browserName")
        print(f"=====打开商店：{store_name}=====")

        ret_json = self.open_store(store_id)
        if not ret_json:
            self.task_params['finally_result'] += f'|【{store_name}】 打开失败！'
            self.task_params['finally_status'] = 20
            return

        store_start_info = {**store_info, **ret_json}
        ziniao = ZiNiaoDP(store_start_info)

        if ziniao.page is None:
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        ip_check_url = store_start_info.get("ipDetectionPage")
        if not ip_check_url:
            print("IP检测页地址为空，请升级紫鸟浏览器到最新版")
            ziniao.page.quit()
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        try:
            if ziniao.open_ip_check(ip_check_url):
                print("IP检测通过，开始下载付款报告")
                ziniao.cur_info['switch_data'] = self.task_params.get('switch_data', {})
                ziniao.download_payment_reports()
                if ziniao.cur_info.get('fail_data'):
                    self.task_params['fail_data'].extend(ziniao.cur_info.get('fail_data'))
                    return
                print(f"==============================商店：{store_name} 付款报告下载完成==============================")
        except Exception as e:
            self.task_params['finally_result'] += f'【{store_name}】脚本运行异常:|{e}'
            ziniao.page.get_screenshot(get_err_path(), f'【{store_name}】_{now_str()}')
            print(f"【{store_name}】脚本运行异常:" + traceback.format_exc())
            self.task_params['finally_status'] = 20
            ziniao.cur_info['status'] = 20
        finally:
            finally_status = max(ziniao.cur_info.get("status", 20), ziniao.cur_info.get("finally_status", 20))
            if finally_status == 20:
                self.task_params['finally_result'] += ziniao.cur_info.get("finally_result")
            self.task_params['finally_status'] = max(ziniao.cur_info.get("finally_status", 20), self.task_params['finally_status'])

            ziniao.page.quit()
            print(f"=====脚本执行结束，关闭商店：{store_name}=====")
            self.close_store(store_id)
            return True

    def task_download_invoice(self, store_info, date_range=None):
        """
        为指定店铺下载付款报告
        :param store_info: 店铺信息
        """
        store_id = store_info.get('browserOauth')
        store_name = store_info.get("browserName")
        print(f"=====打开商店：{store_name}=====")

        ret_json = self.open_store(store_id)
        if not ret_json:
            self.task_params['finally_result'] += f'|【{store_name}】 打开失败！'
            self.task_params['finally_status'] = 20
            return

        store_start_info = {**store_info, **ret_json}
        ziniao = ZiNiaoDP(store_start_info)

        ziniao.download_invoice_date_range = date_range or {
            'start_date': '2024-12-01',
            'end_date': '2025-02-28'
        }

        if ziniao.page is None:
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        ip_check_url = store_start_info.get("ipDetectionPage")
        if not ip_check_url:
            print("IP检测页地址为空，请升级紫鸟浏览器到最新版")
            ziniao.page.quit()
            print(f"=====关闭商店：{store_name}=====")
            self.close_store(store_id)
            return

        try:
            if ziniao.open_ip_check(ip_check_url):
                print("IP检测通过，开始下载付款报告")
                ziniao.cur_info['switch_data'] = self.task_params.get('switch_data', {})
                ziniao.download_invoice()
                if ziniao.cur_info.get('fail_data'):
                    self.task_params['fail_data'].extend(ziniao.cur_info.get('fail_data'))
                    return
                print(f"==============================商店：{store_name} 付款报告下载完成==============================")
        except Exception as e:
            self.task_params['finally_result'] += f'【{store_name}】脚本运行异常:|{e}'
            ziniao.page.get_screenshot(get_err_path(), f'【{store_name}】_{now_str()}')
            print(f"【{store_name}】脚本运行异常:" + traceback.format_exc())
            self.task_params['finally_status'] = 20
            ziniao.cur_info['status'] = 20
        finally:
            finally_status = max(ziniao.cur_info.get("status", 20), ziniao.cur_info.get("finally_status", 20))
            if finally_status == 20:
                self.task_params['finally_result'] += ziniao.cur_info.get("finally_result")
            self.task_params['finally_status'] = max(ziniao.cur_info.get("finally_status", 20), self.task_params['finally_status'])

            ziniao.page.quit()
            print(f"=====脚本执行结束，关闭商店：{store_name}=====")
            self.close_store(store_id)
            return True


def load_img_info(url):
    """
    从URL加载图像内容，并获取分辨率和MD5哈希值
    """
    try:
        url = url.replace('http://cos.yxyglobal.com', 'https://img.yxyglobal.com')
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功

        # 读取完整内容
        image_content = response.content
        # 使用BytesIO将二进制数据转换为图像对象
        with Image.open(BytesIO(image_content)) as image:
            fm = image.format
            resolution = image.size  # 返回 (宽度, 高度)
            # 使用image.verify()来检查图像的完整性
            image.verify()
        # 检查图像格式是否在允许的列表中
        if fm not in ['PNG', 'JPEG', 'GIF', 'JPG']:
            raise Exception(f"{url} 未支持的图像类型： {fm}")
        # 计算MD5哈希值
        md5_hash = calculate_md5_hash(image_content)
        suffix = url.rsplit('.', 1)[-1]

        return image_content, resolution, md5_hash, suffix
    except requests.RequestException as e:
        raise Exception(f"下载图像{url}失败： {e}")
    except IOError as e:
        raise Exception(f"打开图像{url}失败： {e}")


def init_column_targets_count():
    # 定义基础映射规则
    base_mapping = {
        'st_search_frequency': '搜索频率排名',
        'st_search_term': '搜索词',
        'report_date': '报告日期'
    }

    # 定义动态生成的映射规则
    dynamic_keys = [
        ('st_top_asin_{index}', '点击量最高的商品 #{index}：ASIN'),
        ('st_top_asin_click_share_{index}', '点击量最高的商品 #{index}：点击份额'),
        ('st_top_asin_conversion_share_{index}', '点击量最高的商品 #{index}：转化份额'),
        ('st_top_asin_title_{index}', '点击量最高的商品 #{index}：商品名称'),
        ('st_top_brand_{index}', '点击量最高的品牌 #{index}'),
        ('st_top_category_{index}', '点击量最高的类别 #{index}')
    ]

    # 生成完整的映射关系
    column_targets_count = []

    # 添加基础映射
    for column, title in base_mapping.items():
        column_targets_count.append({'column': column, 'title': title})

    # 生成动态映射
    for index in range(1, 4):  # 假设有3个最高点击量的商品、品牌和类别
        for key_template, title_template in dynamic_keys:
            column = key_template.format(index=index)
            title = title_template.format(index=index)
            column_targets_count.append({'column': column, 'title': title})

    # 打印生成的映射关系
    # for item in column_targets_count:
    #     print(item)
    return column_targets_count


def excel_to_sql(path=''):
    """
    将Excel文件解压、重命名并导入到SQL数据库中。

    首先调用unzip_and_rename函数对指定的file_path路径下的Excel文件进行解压和重命名。
    然后，将这些处理后的文件从默认的保存路径或提供的file_path路径中读取，并逐个导入到MySQL数据库中。

    参数:
    - file_path (str, optional): Excel文件的路径。默认为空字符串，表示使用默认的保存路径。

    返回:
    无
    """
    # 如果没有提供file_path，则使用默认的保存路径
    save_path = path or r'D:\rpa_download\excel\top-search-terms'
    # 获取所有文件列表
    files = [os.path.join(save_path, f) for f in os.listdir(save_path) if f.endswith('.csv')]
    # 使用tqdm显示进度条，遍历保存路径下的所有文件
    with tqdm(total=len(files), desc='文件处理进度', unit='份') as pbar_down:
        for file_path in files:
            name = os.path.splitext(os.path.basename(file_path))[0]
            pbar_down.set_description_str(f'正在处理文件：{name}')
            # 将Excel文件导入到MySQL数据库中
            db_save_data_from_excel(file_path)
            pbar_down.update(1)


def check_fields_date(threshold_date=None, path=''):
    """
    检查指定路径下是否有文件的创建日期晚于给定的阈值日期。

    参数:
    path: str, 文件或文件夹的路径。
    threshold_date: datetime, 阈值日期，用于比较文件的创建日期。

    返回:
    bool, 如果存在创建日期晚于阈值日期的文件，则返回True，否则返回False。
    """
    threshold_date = threshold_date or datetime.now()
    # 遍历指定路径下的所有文件和文件夹
    path = path or r'D:\rpa_download\excel\top-search-terms'
    for root, dirs, files in os.walk(path):
        if not files: return True
        for file_name in files:
            # 构建文件的完整路径
            file_path = os.path.join(root, file_name)
            # 获取文件的最后修改时间
            modification_time = os.path.getmtime(file_path)
            modification_date = datetime.fromtimestamp(modification_time)

            if threshold_date.date() > modification_date.date():
                # if threshold_date.date() > modification_date.date():# 检查修改时间是否大于指定日期
                # 如果找到修改时间晚于阈值日期的文件，返回True
                logging(f'执行清理文件夹: {path}')
                clear_folder(path)
                return True
    # 如果没有找到创建日期晚于阈值日期的文件，返回False
    return False


def clear_folder(path):
    """
    清空指定文件夹中的所有文件。

    参数:
    path: str, 要清空的文件夹路径。
    """
    # 检查路径是否存在且是一个文件夹
    if not os.path.exists(path) or not os.path.isdir(path):
        raise ValueError(f"路径 {path} 不存在或不是一个文件夹")

    # 遍历文件夹中的所有文件和子文件夹
    for root, dirs, files in os.walk(path):
        for file_name in files:
            # 构建文件的完整路径
            file_path = os.path.join(root, file_name)
            # 删除文件
            os.remove(file_path)


def get_last_saturday(f='t', date=None):
    if date is None:
        date = datetime.today()
    if isinstance(date, str):
        date = dateutil.parser.parse(date)
    date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    # 获取当前日期的星期几（0=Monday, 6=Sunday）
    weekday = date.weekday()
    # 计算距离最近一个周六的天数
    days_to_saturday = (weekday - 5) % 7
    # 计算最近一个周六的日期
    last_saturday = date - timedelta(days=days_to_saturday)
    if f == 'i':
        last_saturday = int(last_saturday.timestamp())
    elif f == 's':
        last_saturday = last_saturday.strftime('%Y-%m-%d')
    return last_saturday


def db_save_data_from_excel(file_path, params=None):
    if params is None:
        params = {}
    table_name = "data_amazon_top_search_terms"  # 数据库中的表名
    file_name = os.path.splitext(os.path.basename(file_path))[0]  # 获取文件名（不带后缀名）
    # if MS.get_one(f'SELECT 1 FROM rpa.`data_amazon_top_search_terms` where datetime >= {get_last_saturday(f="i")} and site = "{file_name}" limit 1'):
    #     logging(f"检测到【{file_name}】已入库！")
    #     return False
    logging(f"正在处理【{file_name}】")
    try:
        df = pd.read_csv(file_path, header=1)  # 读取 xlsx 文件
        df.fillna('', inplace=True)  # 处理 NaN 值

        task_num = len(df)
        params['platform_num'] = task_num
        params['task_num'] = task_num

        columns_old = df.columns.tolist()
        columns_new = [parse_column_name(col, init_column_targets_count()) for col in columns_old]
        # sql_generate_tb_statement(df, columns_new, MS.engine, tb_name=table_name, tb_comment='亚马逊热门搜索词报告', if_create=True)
        df.columns = columns_new

        # 数据表固定字段处理
        df['task_id'] = params.get('task_id') or now_int()
        df['datetime'] = convert_timestamp_column(df, 'report_date')
        df['task_time'] = params.get('task_time') or get_today_zero_timestamp()
        df['user_id'] = params.get('user_id') or 0
        df['username'] = params.get('username') or ''
        df['data_status'] = params.get('data_status') or 1
        df['create_time'] = now_int()

        # 其他数据处理
        df['site'] = params.get('site') or file_name

        unique_columns = 'report_date,site,st_search_frequency,st_search_term'.split(',')
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, unique_columns=unique_columns)
        # MS = MysqlHelper()
        result = insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表
    except Exception as e:
        traceback.print_exc()
        result = f'{file_name} 插入失败，错误信息：{e}'
    return result


def get_err_path():
    err_path = rf'd:/rpa_download/err_img/{datetime.now().year}_{datetime.now().month}/{datetime.now().day}/ziniao/rule'
    return err_path


def insert_or_update_amazon_rule_datas_old(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """

    # 从params字典中获取相关参数
    app_id = params.get('app_id') or 0
    task_id = params.get('task_id') or 0
    date_time = params.get('date_time') or get_today_zero_timestamp()
    task_time = params.get('task_time') or now_int()
    data_status = params.get('data_status') or 0
    user_id = params.get('user_id') or 0
    username = params.get('username') or ''
    create_time = params.get('create_time') or now_int()
    task_num = params.get('task_num') or len(data)
    platform_num = params.get('platform_num') or len(data)
    done_time = params.get('done_time') or now_int()

    status = params.get('status') or 10
    result = params.get('result') or '成功'

    run_num = 1

    # 定义用于插入或更新Amazon数据的SQL语句,确保unique_id列是数据库表中的唯一键或主键，以利用ON DUPLICATE KEY UPDATE特性
    sql_upsert_data = """
        INSERT INTO `data_amazon_rule`(
            `unique_id`,`app_id`,`task_id`,`datetime`,`task_time`,`data_status`,`user_id`,`username`,`store`,`needs`,`brand`,`img`,`title`,`asin`,`sku`,`sales`,`status`,`remark`,`invalid`,`create_time`,`site`
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            `app_id`=VALUES(`app_id`),
            `task_id`=VALUES(`task_id`),
            `datetime`=VALUES(`datetime`),
            `task_time`=VALUES(`task_time`),
            `data_status`=VALUES(`data_status`),
            `user_id`=VALUES(`user_id`),
            `username`=VALUES(`username`),
            `store`=VALUES(`store`),
            `needs`=VALUES(`needs`),
            `brand`=VALUES(`brand`),
            `img`=VALUES(`img`),
            `title`=VALUES(`title`),
            `asin`=VALUES(`asin`),
            `sku`=VALUES(`sku`),
            `sales`=VALUES(`sales`),
            `status`=VALUES(`status`),
            `remark`=VALUES(`remark`),
            `invalid`=VALUES(`invalid`),
            `site`=VALUES(`site`)
    """

    # 使用列表推导式准备插入数据的参数列表
    insert_params = [
        (
            calculate_md5_hash(''.join(item.get(k) or '' for k in ['store', 'asin', 'sku', 'site'])),  # 拼接unique_id
            app_id,
            task_id,
            date_time,
            task_time,
            data_status,
            user_id,
            username,
            item.get('store') or '',
            item.get('needs') or '',
            item.get('brand') or '',
            item.get('img') or '',
            item.get('title') or '',
            item.get('asin') or '',
            item.get('sku') or '',
            item.get('sales') or 0,
            item.get('status') or '',
            item.get('remark') or '',
            item.get('invalid') or 0,
            create_time,
            item.get('site') or '',
        ) for item in data
    ]

    # 如果有数据需要插入，则执行插入操作
    if insert_params:
        # 执行插入操作
        rs = MS.insert_many(sql_upsert_data, insert_params)
        # 根据插入结果设置状态和结果信息
        f_status, f_result = (10, '成功') if rs > 0 else (10, '无新增或需要更新的数据') if rs == 0 else (20, '异常')
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID-{task_id};抓取{task_num}条数据;保存{result}')
    else:
        f_status, f_result = (10, '无数据入库')

    if f_status == 20:
        if MS.err:
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send(None, msg, MS.err)

    if f_status == 20:
        result += f' |入库提示:{f_result}'
        status = 20
    elif f_status == 10 and '成功' not in f_result:
        result += f' |入库提示:{f_result}'
    return result, status


def insert_or_update_amazon_rule_datas(data, params=None):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """
    if params is None:
        params = {}
    table_name = "data_amazon_rule"  # 数据库中的表名

    # 从params字典中获取相关参数
    app_id = params.get('app_id') or 0
    task_id = params.get('task_id') or 0
    date_time = params.get('date_time') or get_today_zero_timestamp()
    task_time = params.get('task_time') or now_int()
    data_status = params.get('data_status') or 0
    user_id = params.get('user_id') or 0
    username = params.get('username') or ''
    create_time = params.get('create_time') or now_int()
    task_num = params.get('task_num') or len(data)
    platform_num = params.get('platform_num') or len(data)
    done_time = params.get('done_time') or now_int()

    status = params.get('status') or 10
    result = params.get('result') or '成功'

    try:
        df = pd.DataFrame(data)
        # 去除百分比,转换类型 应用该函数到每一列
        df = remove_percent_signs(df)

        params['platform_num'] = platform_num
        params['task_num'] = task_num

        df['task_id'] = task_id
        df['datetime'] = datetime
        df['username'] = username
        df['user_id'] = user_id
        df['data_status'] = data_status

        unique_columns = 'store,asin,sku,site'.split(',')
        # 创建一个新列，将 unique_columns 中的值组合成单个字符串
        df = generate_unique_id(df, unique_columns)
        df['create_time'] = create_time

        save_result = insert_data_from_pd(table_name, df, sql_conn=MS)  # 更新数据表
    except Exception as e:
        traceback.print_exc()
        save_result = f'{params.get("task_id")} 插入失败，错误信息：{e}'

    if save_result is not True:
        # 根据插入结果设置状态和结果信息
        status = 20
        result += save_result
    else:
        logging(f'当前应用ID:{app_id};任务ID-{task_id};抓取{task_num}条数据;保存{result}')

    return result, status


def decode_base64(encoded_str):
    try:
        decoded_bytes = base64.b64decode(encoded_str)
        decoded_str = decoded_bytes.decode('utf-8')
        return decoded_str
    except Exception as e:
        return encoded_str


def find_ziniao_port(process_name="SuperBrowser.exe"):
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        if proc.info['name'] and process_name in proc.info['name']:
            cmdline = proc.info['cmdline']
            if cmdline:
                for arg in cmdline:
                    # 解码 Base64 编码的参数
                    decoded_arg = decode_base64(arg)
                    match = re.search(r'-port=(\d+)', decoded_arg)
                    if match:
                        port = match.group(1)
                        # print(f"Process: {proc.info['name']} (PID: {proc.info['pid']})")
                        # print(f"Port: {port}")
                        return port
    print(f"未找到运行中的进程名 {process_name}")
    return None


def delete_all_cache():
    """
    删除所有店铺缓存
    非必要的，如果店铺特别多、硬盘空间不够了才要删除
    """
    local_appdata = os.getenv('LOCALAPPDATA')
    cache_path = os.path.join(local_appdata, 'SuperBrowser')
    if os.path.exists(cache_path):
        shutil.rmtree(cache_path)


def parse_task_data():
    # 站点映射 法国、西班牙、意大利、德国、比利时、波兰、荷兰、英国、瑞典

    file = '店铺账号名称0218.xlsx'
    # 提取紫鸟店铺账号名称，站点
    df = pd.read_excel(file)
    stores = df['紫鸟名称']
    sites = df['站点']
    switch_data = {}
    for store, site in zip(stores, sites):
        site_list = site.split('、')
        switch_data[store] = site_list
    return switch_data


def clean_duplicate_files(down_path):
    """清理带下划线数字的重复文件"""
    logging("开始清理重复文件...")
    for file in os.listdir(down_path):
        if file.endswith('.pdf'):
            # 检查文件名是否包含下划线+数字的模式
            match = re.search(r'(.+)_\d+\.pdf$', file)
            if match:
                file_path = os.path.join(down_path, file)
                try:
                    os.remove(file_path)
                    logging(f"删除重复文件: {file}")
                except Exception as e:
                    logging(f"删除文件失败 {file}: {e}")
            if '&&' in file:
                file_path = os.path.join(down_path, file)
                try:
                    os.remove(file_path)
                    logging(f"删除无站点文件: {file}")
                except Exception as e:
                    logging(f"删除文件失败 {file}: {e}")


def get_site_variations(site):
    """获取站点的所有可能变体（中英文）"""
    site_variations = {site}  # 添加原始站点名

    # 英文到中文的映射
    en_to_cn = {
        'fr': '法国',
        'es': '西班牙',
        'it': '意大利',
        'de': '德国',
        'be': '比利时',
        'pl': '波兰',
        'nl': '荷兰',
        'uk': '英国',
        'se': '瑞典'
    }

    # 中文到英文的映射
    cn_to_en = {v: k for k, v in en_to_cn.items()}

    # 添加对应的变体
    if site.lower() in en_to_cn:
        site_variations.add(en_to_cn[site.lower()])
    elif site in cn_to_en:
        site_variations.add(cn_to_en[site])

    return site_variations


def run_task_download_payment_reports():
    manager = StoreManager(max_workers=7)
    browser_list = manager.get_browser_list()
    if browser_list is None:
        fsmsg.send("亚马逊后台店铺付款报告下载", "商店列表为空")
        exit()

    # 获取店铺站点映射数据
    switch_data = parse_task_data()

    # 计算理论总文件数
    total_expected_files = sum(len(sites) for sites in switch_data.values())  # 每个站点2个文件

    # 检查已下载文件，剔除已完成的店铺站点
    down_path = pdf_temp_download_path
    if not os.path.exists(down_path):
        os.makedirs(down_path)

    # 首先清理重复文件
    clean_duplicate_files(down_path)

    # 收集已存在的文件信息
    existing_files = set()
    current_file_count = 0
    for file in os.listdir(down_path):
        if file.endswith('.pdf'):
            current_file_count += 1
            parts = file.split('&')
            if len(parts) >= 3:  # 确保文件名格式正确
                store = parts[0]
                site = parts[1]
                year_month = parts[2].split('.')[0]  # 移除.pdf后缀

                # 获取站点的所有可能变体
                site_variations = get_site_variations(site)

                # 将所有可能的组合添加到已存在集合中
                for site_var in site_variations:
                    existing_files.add(f"{store}-{site_var}-{year_month}")

    # 更新待处理的店铺站点列表
    filtered_switch_data = {}
    remaining_files_count = 0
    for store, sites in switch_data.items():
        remaining_sites = []
        for site in sites:
            # 检查 年-月 是否都已存在
            site_variations = get_site_variations(site)
            needs_processing = False
            missing_files = 0
            for year_month in ['2025-2']:
                if not any(f"{store}-{site_var}-{year_month}" in existing_files
                           for site_var in site_variations):
                    needs_processing = True
                    missing_files += 1

            if needs_processing:
                remaining_sites.append(site)
                remaining_files_count += missing_files

        if remaining_sites:
            filtered_switch_data[store] = remaining_sites

    # 计算进度
    completion_percentage = (current_file_count / total_expected_files) * 100

    # 记录进度信息
    progress_msg = (
        f"下载进度统计：\n"
        f"理论总文件数：{total_expected_files} 份\n"
        f"已下载文件数：{current_file_count} 份\n"
        f"待下载文件数：{remaining_files_count} 份\n"
        f"当前完成进度：{completion_percentage:.2f}%"
    )
    logging(progress_msg)

    manager.task_params['switch_data'] = filtered_switch_data
    store_name_list = list(filtered_switch_data.keys())

    # 找出store_name_list中存在于browser_list的店铺
    store_list = []
    no_store_list = []
    for store_name in store_name_list:
        store = next((x for x in browser_list if store_name == x['browserName']), None)
        if store:
            store_list.append(store)
        else:
            no_store_list.append(store_name)

    if no_store_list:
        logging(f'未找到的店铺：{no_store_list}')

    if not store_list:
        msg = f"所有报告已下载完成，无需处理\n{progress_msg}"
        logging(msg)
        fsmsg.send("亚马逊后台店铺付款报告下载", msg)
        exit()
        return

    # 使用多进程处理店铺
    failed_tasks = []
    with concurrent.futures.ProcessPoolExecutor(max_workers=manager.max_workers) as executor:
        futures = {executor.submit(manager.task_download_payment_reports, store): store for store in store_list}
        for future in concurrent.futures.as_completed(futures):
            store = futures[future]
            try:
                future.result()
            except Exception as e:
                error_msg = f"店铺 {store['browserName']} 处理失败: {e}"
                logging(error_msg)
                traceback.print_exc()
                failed_tasks.append(error_msg)
                manager.task_params['fail_data'].append(error_msg)

    # 再次清理重复文件
    clean_duplicate_files(down_path)

    # 重新统计最终文件数
    final_file_count = sum(1 for file in os.listdir(down_path) if file.endswith('.pdf'))
    final_completion_percentage = (final_file_count / total_expected_files) * 100

    # 汇总结果
    total_stores = len(store_list)
    failed_stores = len(manager.task_params.get('fail_data', []))
    success_stores = total_stores - failed_stores

    # 保存失败记录供下次使用
    if manager.task_params.get('fail_data'):
        fail_log_path = os.path.join(down_path, 'failed_tasks.txt')
        with open(fail_log_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(manager.task_params['fail_data']))

    summary_msg = (
        f"付款报告下载任务完成\n"
        f"总店铺数: {total_stores}\n"
        f"成功任务数: {success_stores}\n"
        f"失败任务数: {failed_stores}\n"
        f"未找到店铺: {len(no_store_list)}\n"
        f"\n最终下载统计：\n"
        f"理论总文件数：{total_expected_files} 份\n"
        f"实际下载文件数：{final_file_count} 份\n"
        f"最终完成进度：{final_completion_percentage:.2f}%"
    )

    if manager.task_params.get('fail_data'):
        summary_msg += f"\n失败详情已保存至: {fail_log_path}"

    logging(summary_msg)
    fsmsg.send("亚马逊后台店铺付款报告下载", summary_msg)
    input('按回车键退出')
    exit()


def run_task_download_invoice(date_range=None, max_workers=7):
    manager = StoreManager(max_workers=max_workers)
    browser_list = manager.get_browser_list()
    if browser_list is None:
        fsmsg.send("亚马逊后台店铺付款报告下载", "商店列表为空")
        exit()

    # 获取店铺站点映射数据
    switch_data = parse_task_data()
    manager.task_params['switch_data'] = switch_data
    store_name_list = list(switch_data.keys())

    # 找出store_name_list中存在于browser_list的店铺
    store_list = []
    no_store_list = []
    for store_name in store_name_list:
        store = next((x for x in browser_list if store_name == x['browserName']), None)
        if store:
            store_list.append(store)
        else:
            no_store_list.append(store_name)

    if no_store_list:
        logging(f'未找到的店铺：{no_store_list}')

    if not store_list:
        msg = f"所有报告已下载完成，无需处理\n"
        logging(msg)
        fsmsg.send("亚马逊后台店铺付款报告下载", msg)
        exit()
        return

    # 使用多进程处理店铺
    failed_tasks = []
    with concurrent.futures.ProcessPoolExecutor(max_workers=manager.max_workers) as executor:
        futures = {executor.submit(manager.task_download_invoice, store, date_range): store for store in store_list}
        for future in concurrent.futures.as_completed(futures):
            store = futures[future]
            try:
                future.result()
            except Exception as e:
                error_msg = f"店铺 {store['browserName']} 处理失败: {e}"
                logging(error_msg)
                traceback.print_exc()
                failed_tasks.append(error_msg)
                manager.task_params['fail_data'].append(error_msg)

    # 重新统计最终文件数
    final_file_count = sum(1 for file in os.listdir(invoice_download_path) if file.endswith('.pdf'))

    # 汇总结果
    total_stores = len(store_list)
    failed_stores = len(manager.task_params.get('fail_data', []))
    success_stores = total_stores - failed_stores

    # 保存失败记录供下次使用
    if manager.task_params.get('fail_data'):
        fail_log_path = os.path.join(invoice_download_path, 'failed_tasks.txt')
        with open(fail_log_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(manager.task_params['fail_data']))

    summary_msg = (
        f"任务完成\n"
        f"总店铺数: {total_stores}\n"
        f"成功任务数: {success_stores}\n"
        f"失败任务数: {failed_stores}\n"
        f"未找到店铺: {len(no_store_list)}\n"
        f"\n最终下载统计：\n"
        f"实际下载文件数：{final_file_count} 份\n"
    )

    logging(summary_msg)
    fsmsg.send("亚马逊后台店铺付款报告下载", summary_msg)
    input('按回车键退出')
    exit()


if __name__ == "__main__":
    # run_task_download_payment_reports()
    run_task_download_invoice({
        'start_date': '2024-12-01',
        'end_date': '2025-02-28'
    }, 7)
    manager = StoreManager(max_workers=1)

    browser_list = manager.get_browser_list()
    if browser_list is None:
        fsmsg.send("亚马逊后台店铺合规性资料采集", "商店列表为空")
        exit()

    """打开第一个店铺运行脚本"""
    store_name = 'AM_YLL'
    # switch_data = parse_task_data()
    switch_data = {
        'AM_ZJY张静雯': list(site_map.keys()),
    }
    store_name = list(switch_data.keys())[0]
    store = next((x for x in browser_list if store_name == x['browserName'])) or exit('未找到指定店铺')
    manager.task_params['switch_data'] = switch_data
    manager.task_download_invoice(store)
    # manager.task_download_payment_reports(store)
    print(manager.task_params.get('fail_data'))
    # manager.task_download_top_search_terms(store)
    # manager.use_one_browser_run_task(store)
    # manager.task_goods_diy(store)
    """循环打开所有店铺运行脚本"""
    # manager.use_all_browser_run_task(browser_list)
    """关闭客户端"""
    # manager.get_exit()
