from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

MS
# 定义映射字典
day_to_week = {
    '第一天': 'week_1',
    '第二天': 'week_2',
    '第三天': 'week_3',
    '第四天': 'week_4',
    '第五天': 'week_5',
    '第六天': 'week_6',
    '第七天': 'week_7'
}
df = pd.read_excel(r'D:\Downloads\类目节点-0813.xlsx')
# 将列名转换为小写
df.columns = df.columns.str.lower()

# 使用ffill方法处理缺失值
df.fillna(method='ffill', inplace=True)

# 使用map函数根据映射字典转换“爬取频率”列
df['爬取频率2'] = df['爬取频率'].map(day_to_week)
# print(df)
data = []
unque_id = []
success_count = 0
for index, row in df.iterrows():
    frequency = row['爬取频率2']
    title = row['品类']
    id = row['类目节点收集']
    url = f'https://www.amazon.de/s?rh=n:{id},p_36:500-2000'
    if id in unque_id:
        # print(f'{frequency} {id} {title} 已存在')
        continue
    unque_id.append(id)
    ins_cron_sql = "INSERT INTO `rpa`.`tasks_cron` (`app_id`, `platform`, `site`, `title`, `url`, `user_id`, `username`, `page`, `crawl_frequency`, `create_time`) VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    item = (1, 'amazon', 'de', title, url, 37, '梁桂丹', 10, frequency, now_int())
    rs = MS.insert(ins_cron_sql, item)
    if rs: success_count += 1

    # insert_task_sql = """INSERT INTO `rpa`.`task_goods` (`app_id`, `pid`, `platform`, `site`, `title`, `url`, `user_id`, `username`, `page`, `start_time`, `end_time`, `datetime`, `create_time`) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);
    #  """
    # item_1 = (1, 0, 'amazon', 'de', title, url, 37, '梁桂丹', 10, get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int())
    # rs = MS.insert(insert_task_sql, item_1)

    sql = f"call CreateTaskGoods(1,'amazon', 0, 'de', '{title}', '{url}', 37, '梁桂丹', 10, 0);"
    # MS.cursor.callproc('CreateTaskGoods', args=(1, 'amazon', 0, 'de', title, url, 37, '梁桂丹', 10, 0))
    # rs = MS.update(sql)
    data.append(item)
    # exit()
print(f'成功插入 {success_count} 条数据')
# print(len(data))
# print(data)
