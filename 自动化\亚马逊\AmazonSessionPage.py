# -*- coding:UTF-8 -*-
# @FileName  :AmazonSessionPage.py
# @Time      :2024/12/11 14:04
# <AUTHOR>
import time

from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import *
from utils_mrc.SpiderTools import *
from utils_mrc.RedisClient import *
import amazoncaptcha
import json
from functools import wraps
from utils_mrc.passTLS import tls_session

db_cookie = RedisClient('cookies', 'amazon')
db_cookie_us = RedisClient('cookies', 'amazonUS')
db_account = RedisClient('accounts', 'amazon')


def retry_on_exception(retry_count=3, msg='数据解析异常'):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            attempts = 0
            while True:
                try:
                    return func(self, *args, **kwargs)
                except Exception as e:
                    # 如果页面检查通过，进行重试
                    attempts += 1  # 增加重试次数
                    check_result = self.check_page()
                    if check_result is None:
                        self.cur_info['result'] += f' |[第{self.cur_info.get("cur_page")}页]{msg}：{e}'
                        self.cur_info['status'] = 20
                        return []
                    elif check_result is False:
                        return []
                    if attempts > retry_count:
                        traceback.print_exc()
                        self.cur_info['result'] += f' |[第{self.cur_info.get("cur_page")}页]{msg}：{e}'
                        self.cur_info['status'] = 20
                        return []  # 超过最大重试次数，返回空列表

        return wrapper

    return decorator


class AmazonSessionPage(SessionPageManager):

    def __init__(self, port=9222, proxy_type=1):
        super().__init__(proxy_type)
        self.page.set.headers(super().headers)
        # self.page = SessionPage(tls_session)
        # self.page.set.headers(super().headers)
        self.cycle_proxy_type = IPPool.cycle_proxy_type
        self.cycle_proxy_type2 = IPPool.cycle_proxy_type
        self.port = port
        self.init_cookies()

        self.cur_cookie_site = ''
        self.cur_us_user = ''
        self.cookies = {}
        self.us_cookies = {}
        self.cur_country = None
        self.cur_address = None
        self.cur_info = {}
        self.task_params = {}
        self.address_proxy_success = True
        self.address_proxy_type = 2

    def init_cookies(self):
        # 获取端口号
        port = sys.argv[1] if len(sys.argv) > 1 and sys.argv[1].isdigit() else self.port
        if port == 9555:
            content = '{}'
            cookies = json.loads(content or '{}')
            self.page.set.cookies(cookies)
            self.cookies = cookies
        elif port:
            logging(f"使用的用户数据为: {port}")
            self.port = port
            db_account.set(port, '')  # 添加账号
            content = db_cookie.get(port)  # 获取账号cookies信息
            # 解析 cookies
            cookies = json.loads(content or '{}')
        
            self.page.set.cookies(cookies)
            self.cookies = cookies

    def get_port_cookies(self):
        content = db_cookie.get(self.port)  # 获取账号cookies信息
        # 解析 cookies
        cookies = json.loads(content or '{}')
        self.page.set.cookies(cookies)
        self.cookies = cookies
        return cookies

    def change_address(self):
        if self.check_addr_code():
            return True
        self.cur_info['url'] = url = self.page.url
        html = self.page.html
        if 'glow-ingress-line2' not in html:
            print('当前页面未检测到地址元素！')
            return
        address = self.page('#glow-ingress-line2')
        addressText = address.text.strip()
        domain = urlparse(url).netloc
        matchFound = False
        zipcode = '90001'
        domain_code = domain.split("amazon.")[1]
        value = AmazonConfig.SITE_CODE[domain_code]
        self.cur_country = AmazonConfig.COUNTRY_SITE_SPRITE[domain_code]
        self.cur_address = addressText

        if isinstance(value, list):  # 判断是否为列表（Python 中的数组）
            if not any(item in addressText for item in value):  # 如果值是列表，检查列表中的任何元素是否包含在 addressText 中
                matchFound = True
                zipcode = value[0]
        else:  # 如果值不是列表
            if value not in addressText:  # 直接比较值是否在 addressText 中
                zipcode = value
                matchFound = True

        if matchFound:
            print(f'当前站点：{self.cur_country},当前地址为：{addressText},与目标地址:{zipcode}不匹配。正在修改地址信息...', end=' ')
            # 获取数据
            get_data = self.page("#nav-global-location-data-modal-action").attr('data-a-modal')
            modal_data = json.loads(get_data)
            url2 = modal_data["url"]
            anti_csrftoken_a2z = modal_data['ajaxHeaders']["anti-csrftoken-a2z"]

            self.headers_change_addr = {
                'anti-csrftoken-a2z': anti_csrftoken_a2z,
                'user-agent': self.so.headers['user-agent'],
                "accept": "text/html,*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "viewport-width": "1912",
                "x-requested-with": "XMLHttpRequest"
            }
            base_url = f"https://{domain}{url2}"

            # self.set_address_proxy()  # 切换站点需要暂时关闭代理，使用本地IP，否则可能不返回token数据
            self.page.get(base_url, headers=self.headers_change_addr)
            csrf_token_match = re.search(r'CSRF_TOKEN\s*:\s*"([^"]+)"', self.page.html)
            if not csrf_token_match:
                logging('页面信息不完整，可能是TLS指纹被识别！未找到CSRF_TOKEN,下一次将切换代理重试！')
                self.address_proxy_success = False
                self.set_address_proxy()  # 切换站点需要暂时关闭代理，使用本地IP，否则可能不返回token数据
                return
            csrf_token = csrf_token_match.group(1)
            page_type_match = re.search(r"pageType=([^&]+)", base_url)
            if not page_type_match:
                logging('页面信息不完整，可能是TLS指纹被识别！未找到pageType')
                self.address_proxy_success = False
                self.set_address_proxy()  # 切换站点需要暂时关闭代理，使用本地IP，否则可能不返回token数据
                return
            page_type = page_type_match.group(1)
            storecontext = "wireless" if "/dp" in self.cur_info['url'] else "generic"

            change_result = self.change_zipcode(zipcode, storecontext, page_type, csrf_token, domain)
            self.open_proxy(self.proxy_type)  # 切换站点后继续启用初始代理
            return change_result

    def set_address_proxy(self):
        if not self.address_proxy_success:
            self.address_proxy_type = next(self.cycle_proxy_type2)
        self.open_proxy(self.address_proxy_type)

    def change_zipcode(self, zipcode: str, storecontext: str, page_type: str, csrf_token: str, domain: str):
        url = f"https://{domain}/portal-migration/hz/glow/address-change?actionSource=glow"

        data = {
            "locationType": "LOCATION_INPUT",
            "zipCode": zipcode,
            "storeContext": storecontext,
            "deviceType": "web",
            "pageType": page_type,
            "actionSource": "glow"
        }
        headers = {
            **self.headers_change_addr,
            "anti-csrftoken-a2z": csrf_token,
            "content-type": "application/json",
            "origin": domain,
        }

        self.page.post(url, headers=headers, json=data)
        '''
        {"isValidAddress":1,"isAddressUpdated":1,"sembuUpdated":1,"isTransitOutOfAis":1,"successful":1,"address":{"countryCode":"DE","zipCode":"14199","state":"","city":"Berlin","district":"","addressId":null,"obfuscatedId":null,"locationType":"LOCATION_INPUT","addressLabel":null,"addressLine1":"","isDefaultShippingAddress":0,"isAccountAddress":0}}
        '''

        result = self.page.json
        if result and result.get('successful'):
            self.page.get(self.cur_info['url'])
            self.process_captchas()
            self.check_addr_code()
            print(f'{self.cur_country}地址已切换成功！')
            self.address_proxy_success = True
            self.save_cookies_to_db()
            return True
        print(f'{self.cur_country}地址切换失败！')
        self.address_proxy_success = False
        self.set_address_proxy()  # 切换站点需要暂时关闭代理，使用本地IP，否则可能不返回token数据

    def save_cookies_to_db(self):
        cookies = self.page.cookies()
        cookies_str = json.dumps(cookies)
        if self.cur_cookie_site == 'us':
            db_cookie_us.set(self.cur_us_user, cookies_str)
            logging(f'{self.cur_us_user} cookies已保存。')
        if self.port:
            db_cookie.set(self.port, cookies_str)
            logging(f'{self.port} cookies已保存。')

    def get_us_cookies(self, is_change=False):
        if not self.us_cookies or is_change:
            obj = db_cookie_us.random_obj()
            self.cur_us_user = obj.get('name') or ''
            us_cookies_str = obj.get('value') or '{}'
            us_cookies = json.loads(us_cookies_str)
            self.us_cookies = us_cookies
            return us_cookies
        return self.us_cookies

    def goto(self, url, show_errmsg=False, retry=None, interval=None, timeout=None, **kwargs):
        if '地址切换失败' in self.cur_info.get('result', ''):
            err = f'{self.cur_info["result"]}，启动方案2切换cookies！'
            logging(err)
            fsmsg.send(None, err)
            if '.amazon.com' in url and self.cur_cookie_site != 'us':
                self.get_us_cookies()
                self.page.set.cookies.clear()
                self.page.set.cookies(self.us_cookies)
                self.cur_cookie_site = 'us'
            elif self.cur_cookie_site == 'us' and '.amazon.com' not in url:
                self.page.set.cookies.clear()
                self.page.set.cookies(self.get_port_cookies())
                self.cur_cookie_site = ''
        self.page.get(url, show_errmsg=False, retry=None, interval=None, timeout=None, **kwargs)

        check_result = self.check_page()
        if check_result is None:
            raise Exception(self.cur_info['result'])

    def check_addr_code(self):
        if '.amazon.' in self.page.url and 'glow-ingress-block' in self.page.html:
            ingress = self.page('#glow-ingress-line2') or self.page('#glow-ingress-block')
            element_text = ingress.text
            isin = any(item in element_text for item in AmazonConfig.ADDRESS_CODE)
            if not isin:
                print(f'当前地址：{element_text}与目标地址不匹配,需要修改！')
            return isin

    def check_page(self):
        error = ''
        cur_url = self.page.url
        for i1 in range(3):  # 验证码最大重试3次
            self.cur_info['url'] = cur_url

            href = parse_url_href(cur_url)
            html = self.get_html()

            if not html and not self.page.response:
                self.page.get(cur_url)
                error = f' |{self.cur_info.get("cur_page")}页：空页面，请检查链接或代理！'
                continue
            self.process_captchas()  # 检测处理验证码

            count_503 = 0
            while 'ref=cs_503_link' in html or (self.page.response and self.page.response.status_code == 503):
                count_503 += 1
                if count_503 <= 1: print('503页面', end=' ')
                if count_503 % 3 == 0:
                    self.open_proxy(next(self.cycle_proxy_type))
                url_503_1 = f'{href}/ref=cs_503_logo'
                url_503_2 = f'{href}/ref=cs_503_link'
                url_503_3 = f'{href}/dogsofamazon/ref=cs_503_d'
                hrefs = [url_503_1, url_503_2, url_503_3, href]
                self.page.get(random.choice(hrefs))
                self.process_captchas()
                time.sleep(2)
                self.page.get(cur_url)
                self.process_captchas()
                html = self.get_html()

            if count_503:
                self.open_proxy(self.proxy_type)
                print(f'503次数:{count_503}')

            if 'ref=cs_404_link' in html or self.page.response.status_code == 404:
                self.cur_info['result'] = f' |404页面'
                self.cur_info['cur_asin_result'] = '404页面'
                self.cur_info['status'] = 20
                return False

            if not self.change_address():
                self.page.get(cur_url)
                error = f' |[第{self.cur_info.get("cur_page", 1)}页] 地址切换失败'
                continue

            if '/s?' in self.page.url and self.page('c=div>span[class="a-size-medium a-color-base"]'):  # 搜索页面的操作
                self.cur_info['result'] = f' |[第{self.cur_info.get("cur_page", 1)}页] 搜索页面未返回结果'
                self.cur_info['status'] = 10
                logging(self.cur_info['result'])
                return False

            if not html and not self.page.response:
                self.page.get(cur_url)
                error = f' |{self.cur_info.get("cur_page")}页：空页面，请检查链接或代理！'
                continue

            error = ''
            return True  # 已检测到可能的异常全处理完则跳出页面页面检测
        else:
            self.cur_info['result'] += f' |{error}' if error else f' |[第{self.cur_info.get("cur_page", 1)}页] 页面异常处理失败！'
            self.cur_info['status'] = 20
            return

    def process_captchas(self):
        cur_url = self.page.url
        href = parse_url_href(cur_url)
        for i in range(30):
            if 'id="captchacharacters"' not in self.page.html:
                return True
            print('检测到验证码页面...', end=' ')
            img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
            # print(img_url)
            print(f'验证码识别中...', end=' ')
            try:
                captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
            except:
                self.page.get(cur_url)
                continue
            print('本次识别结果为：', captcha_result)

            amzn = self.page('c=input[name="amzn"]').attr('value')
            amznr = self.page('c=input[name="amzn-r"]').attr('value')
            # print(amzn, amznr)
            params = {
                "amzn": amzn,
                "amzn-r": amznr,
                "field-keywords": captcha_result
            }
            self.page.get(f"{href}/errors/validateCaptcha", params=params)
        self.cur_info['result'] = f' |验证码处理失败！'
        self.cur_info['status'] = 20

    def get_site_by_url(self, url=None):
        if not url:
            url = self.page.url
        if not url:
            logging('获取当前页面地址为空，请确认已访问站点地址,已默认为德国站点')
            return 'de'
        domain = urlparse(url).netloc
        site = domain.split('.')[-1]
        site = AmazonConfig.COUNTRY_SITE_SPRITE.get(site) or site
        return site

    def get_url_info(self, tab=None):
        tab = tab or self.page
        cur_url = tab.url
        domain = urlparse(cur_url).netloc
        site = domain.split('.')[-1]
        return cur_url, site

    def reset_language(self, tab=None, site=None):
        tab = tab or self.page
        cur_url, cur_site = self.get_url_info(tab)
        site = site or cur_site
        site_code = get_key_by_value(site, AmazonConfig.COUNTRY_SITE_SPRITE)
        language = AmazonConfig.LANGUAGES.get(site_code, '')
        new_url = update_url_param(cur_url, 'language', language) if language else cur_url
        cookies_list = tab.cookies()
        lc_ = next(filter(lambda x: x['name'].startswith('lc-'), cookies_list), {})
        if lc_ and site_code.lower() not in lc_['value'].lower():
            tab.set.cookies.remove(name=lc_['name'])
            tab.get(new_url)

    def test_page(self):
        self.page.get('https://www.amazon.co.uk/')
        # self.page.get('https://www.amazon.com/')
        self.check_page()
        self.check_addr_code()


def init_fetch_info():
    return {
        'result': '', 'status': 10, 'page_count': 0, 'end_page_num': 0, 'total': 0, 'cur_page': 1, 'real_page': 1,
        'all_loc_index': 0, 'cur_asin_result': ''
    }


def insert_amazon_listing(data_list: list, params: dict, table_name: str = None):
    '''
    :param data_list: 数据列表
    :param params: 任务参数
    :param table_name: 表名
    '''
    table_name = table_name or 'data_amazon_asin_attr'

    task_id = params.get('task_id', 0)
    app_id = params.get('app_id', 0)
    status = max(params.get('finally_status') or 0, params.get('status') or 0) or 10
    result = params.get('result') or '成功'
    task_num = len(data_list)
    platform_num = params.get('platform_num', task_num)
    done_time = params.get('done_time', 0)
    run_num = params.get('run_num', 1)

    if not data_list:
        # 更新任务状态
        MS.update(
                f'update `task_listing` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
                (status, result, platform_num, task_num, run_num, done_time, task_id)
        )
        return
    # 转换为DataFrame
    df = pd.DataFrame(data_list)

    # JSON字段处理
    json_columns = [
        'tech_spec',
    ]

    for col in json_columns:
        if col in df.columns:
            df[col] = df[col].apply(lambda x: json.dumps(x) if not pd.isna(x) else "{}")

    # 添加任务相关字段
    df['app_id'] = params.get('app_id', 0)
    df['task_id'] = task_id
    df['datetime'] = params.get('datetime', get_today_zero_timestamp())
    df['task_time'] = params.get('task_time', now_int())
    df['data_status'] = params.get('data_status', 0)
    df['user_id'] = params.get('user_id', 0)
    df['username'] = params.get('username', '')
    df['create_time'] = params.get('create_time', now_int())
    # # 删除ID 使用自增ID
    # df.drop(columns=['id'], inplace=True)
    unique_columns = 'asin,site'.split(',')
    df = generate_unique_id(df, unique_columns, split_char='_')

    # 执行插入
    result_ins = insert_data_from_pd(table_name, df, sql_conn=MS)

    # 如果有数据需要插入，则执行插入操作
    if result_ins is not True:
        msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
        logging(msg)
        fsmsg.send('亚马逊Listing数据采集', msg, MS.err)
        result += f' |入库提示:{result_ins}'
        status = 20
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID:{task_id};抓取{task_num}条数据;操作{task_num}条{result}') if task_num else ''

    # 更新任务状态
    MS.update(
            f'update `task_listing` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


def insert_or_update_amazon_asin_loc_index(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """
    # 如果有数据需要插入，则执行插入操作
    data_table_name = "rpa.data_amazon_asin_loc"  # 数据库中的表名
    task_table_name = "rpa.task_asins_loc"  # 数据库中的表名

    task_id = params.get('task_id') or 0
    status = max(params.get('status') or 0, params.get('status') or 0) or 10
    result = params.get('result') or '成功'
    task_num = params.get('task_num') or len(data)
    platform_num = params.get('platform_num') or len(data)
    done_time = params.get('done_time') or 0
    run_num = 1
    if task_num:
        df = pd.DataFrame(data)

        app_id = params.get('app_id') or 0
        date_time = params.get('date_time') or 0
        task_time = params.get('task_time') or now_int()
        data_status = params.get('data_status') or 0
        user_id = params.get('user_id') or 0
        username = params.get('username') or ''
        site = params.get('site') or ''
        create_time = params.get('create_time') or now_int()

        df['app_id'] = app_id
        df['task_id'] = task_id
        df['datetime'] = date_time
        df['task_time'] = task_time
        df['data_status'] = data_status
        df['user_id'] = user_id
        df['username'] = username
        if site: df['site'] = site
        df['create_time'] = create_time
        unique_columns = 'all_loc_index,datetime,keywords'.split(',')
        df = generate_unique_id(df, unique_columns, split_char='_')

        # 执行插入操作
        insert_data_from_pd(data_table_name, df, sql_conn=MS)  # 更新数据表
        # 根据插入结果设置状态和结果信息
        if MS.err:
            f_status, f_result = (20, '异常')
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send('亚马逊广告位数据采集', msg, MS.err)
            result += f' |入库提示:{f_result}'
            status = 20
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID:{task_id};抓取{task_num}条数据{result}') if task_num else ''
    else:
        result += f' |入库提示:无数据入库' if status == 10 else ''

    # 更新任务状态
    MS.update(
            f'update {task_table_name} set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


def insert_or_update_amazon_search_data_by_image(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """
    # 如果有数据需要插入，则执行插入操作
    data_table_name = "rpa.data_amazon_search_data_by_image"  # 数据库中的表名
    task_table_name = "rpa.task_asins_price"  # 数据库中的表名

    task_id = params.get('task_id') or 0
    status = max(params.get('status') or 0, params.get('status') or 0) or 10
    result = params.get('result') or '成功'
    task_num = params.get('task_num') or len(data)
    platform_num = params.get('platform_num') or len(data)
    done_time = params.get('done_time') or 0
    run_num = 1
    if task_num:
        df = pd.DataFrame(data)

        app_id = params.get('app_id') or 0
        date_time = params.get('date_time') or get_today_zero_timestamp()
        task_time = params.get('task_time') or now_int()
        data_status = params.get('data_status') or 0
        user_id = params.get('user_id') or 0
        username = params.get('username') or ''
        site = params.get('site') or ''
        create_time = params.get('create_time') or now_int()

        df['app_id'] = app_id
        df['task_id'] = task_id
        df['datetime'] = date_time
        df['task_time'] = task_time
        df['data_status'] = data_status
        df['user_id'] = user_id
        df['username'] = username
        if site: df['site'] = site
        df['create_time'] = create_time
        unique_columns = 'asin,site,datetime'.split(',')
        df = generate_unique_id(df, unique_columns, split_char='_')

        # 执行插入操作
        insert_data_from_pd(data_table_name, df, sql_conn=MS)  # 更新数据表
        # 根据插入结果设置状态和结果信息
        if MS.err:
            f_status, f_result = (20, '异常')
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send(None, msg, MS.err)
            result += f' |入库提示:{f_result}'
            status = 20
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};任务ID:{task_id};抓取{task_num}条数据{result}') if task_num else ''
    else:
        result += f' |入库提示:无数据入库' if status == 10 else ''

    # 更新任务状态
    MS.update(
            f'update {task_table_name} set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


if __name__ == '__main__':
    # amz = AmazonSessionPage(9556)
    # amz.test_page()
    # print()
    data = [{'status': 10, 'title': 'Cosmeria Silver Irregular Frame Photocard Holder Keychain, Electroplated Y2K ID Card Badge Holder for Students and Office Supplies',
             'platform_account': 'Cosmeria', 'product_desc': '', 'img_url': 'https://m.media-amazon.com/images/I/61IMoVZfEUL.__AC_SY445_SX342_QL70_ML2_.jpg',
             'detail_seller_info': 'Impressum & Info zum Verkäufer\nBusiness-Verkäufer\nGeschäftsname: xuanenxianzhuqishangmaoyouxiangongsi\nGeschäftsart: Unternehmen in Privatbesitz\nHandelsregisternummer: 91422825MADJ1NBG7D\nUStID: DE368631543\nTelefonnummer: +*************\nE-Mail: <EMAIL>\nGeschäftsadresse: \nzhushanzhenjianshelu1haojindianguojiB-1204\nenshitujiazumiaozuzizhizhou\nxuanenxian\nhubeisheng\n445599\nCN\nDieser Verkäufer hat zugesichert, dass er alle geltenden Gesetze einhalten wird. Dazu gehört auch, dass er nur Produkte und Dienstleistungen anbietet, die mit den Richtlinien von Amazon und den geltenden Vorschriften des EU-Rechts übereinstimmen.',
             'site': 'de', 'asin': 'B0DFYSXTPS', 'has_cart': 1, 'url': 'https://www.amazon.de/dp/B0DFYSXTPS'}, {'status': 10,
                                                                                                                'title': 'SLIVERBA 2 Stück Geflochtenes Leder Schlüsselanhänger, Leder Auto Schlüsselanhänger,Schlüsselanhänger für Männer Frauen, für Auto, Motorrad, Fahrrad und Haus Schlüsselband',
                                                                                                                'platform_account': 'Starba',
                                                                                                                'product_desc': 'Produktbeschreibungen \n Verbringen Sie immer Zeit, um Ihre Schlüssel in Ihrem täglichen Leben zu suchen, suchten Sie also etwas, um Ihre Schlüssel zu speichern? Sie wissen nicht, welche Geschenke gesendet werden sollten, also möchten Sie ein ideales generisches Geschenk? \n Richtig, unser gewebter Leder Keychain kann Ihnen helfen, alle Probleme zu lösen. \n Eigenschaften: \n 1. Metallringkomponenten bestehen aus Zinklegierungsmaterialien, und die Teile sind handgewebte Gürtel, elegant und langlebig. Exquisite Verarbeitung, mehrlagige geflochtene Seile Gurte sind sehr stark und langlebig,und es ist auch Schwer zu brechen. \n 2. Praktischer und tragbarer Kfz-Schlüsselanhänger und Zimmer Keychain! Die Schlüsselkette ist stilvoll, nicht nur Dekoration, sondern auch für Thanksgiving, Weihnachten, Valentinstag, Abschluss, Geburtstag, Jubiläum, Hochzeit und Zurück zur Schule, geeignet für alle Arten von Menschen. \n 3. schlüsselanhänger leder Exquisites Aussehen, leicht und leicht zu tragen, bequem zu organisieren Aufbewahrung und Trageschlüssel, das Material ist bequem und weich. \n 4. Geflochtenes Leder Schlüsselanhänger ist geeignet für eine Vielzahl von Menschen, kann als Auto-Schlüsselbund, Outdoor-Sport, Urlaub, Geschenke usw. verwendet werden. \n Spezifikation: \n Material: Zinklegierung + Gewebtes Leder \n Farbe: Schwarz \n Gesamtlänge: 12.2 cm \n Seillänge: 7.5 cm \n Schlüsselanhänger Durchmesser: 3 cm \n Verpackung enthält: 2 * Geflochtenes Leder Schlüsselanhänger \n Hinweis: \n Aufgrund der manuellen Messung und unterschiedlicher Bildschirmeinstellungen lassen Sie bitte einen kleinen Größen- und Farbfehler zu.',
                                                                                                                'img_url': 'https://m.media-amazon.com/images/I/61iRwauaWpL.__AC_SY445_SX342_QL70_ML2_.jpg',
                                                                                                                'detail_seller_info': 'Impressum & Info zum Verkäufer\nBusiness-Verkäufer\nGeschäftsname: shenzhenshi xingbei kejiyouxiangongsi\nGeschäftsart: Unternehmen in Privatbesitz\nHandelsregisternummer: 91440300MA5HU12E5X\nUStID: DE362477709\nTelefonnummer: +8613823578990\nE-Mail: <EMAIL>\nGeschäftsadresse: \n西乡街道蚝业社区兴业路2005号\n宝安互联网产业基地C区C栋603\n深圳市\n宝安区\n广东省\n518000\nCN\nDieser Verkäufer hat zugesichert, dass er alle geltenden Gesetze einhalten wird. Dazu gehört auch, dass er nur Produkte und Dienstleistungen anbietet, die mit den Richtlinien von Amazon und den geltenden Vorschriften des EU-Rechts übereinstimmen.',
                                                                                                                'site': 'de', 'asin': 'B0CPM1KV8B', 'has_cart': 1,
                                                                                                                'url': 'https://www.amazon.de/dp/B0CPM1KV8B'},
            {'status': 10, 'title': 'LUKIUP 24 Stücke Maske Kinder für Kinder Birthday Party Favors,Weihnachten, und Motto-Partys', 'platform_account': 'SANYUNMY',
             'product_desc': '', 'img_url': 'https://m.media-amazon.com/images/I/710fQkgmdQL.__AC_SY445_SX342_QL70_ML2_.jpg',
             'detail_seller_info': 'Impressum & Info zum Verkäufer\nBusiness-Verkäufer\nGeschäftsname: xiaoganshisanyunmaoyiyouxiangongsi\nGeschäftsart: Unternehmen in Privatbesitz\nHandelsregisternummer: 91420900MA49F30883\nUStID: DE334901318\nTelefonnummer: +86 130 9415 0379\nE-Mail: <EMAIL>\nGeschäftsadresse: \n267haoyixinxinju2dong1danyuan101shi\nxiaoganshi\nchengzhanlu\nhubeisheng\n432000\nCN\nDieser Verkäufer hat zugesichert, dass er alle geltenden Gesetze einhalten wird. Dazu gehört auch, dass er nur Produkte und Dienstleistungen anbietet, die mit den Richtlinien von Amazon und den geltenden Vorschriften des EU-Rechts übereinstimmen.',
             'site': 'de', 'asin': 'B09KLDL2LK', 'has_cart': 1, 'url': 'https://www.amazon.de/dp/B09KLDL2LK'}, {'status': 10,
                                                                                                                'title': 'BIVOFU 2PCS Id Lanyards, Strap Abzeichen Lanyards, Schlüsselband Mit Niedlichem Muster, Umhängeband Mit Schlüsselbund, Stilvolles Lanyard Mit Sicherem Clip Und Sanitärinstrument Design',
                                                                                                                'platform_account': 'BIVOFU-EU',
                                                                                                                'product_desc': 'Produktbeschreibungen \nDas 2er Pack Lanyard Schlüsselhalter-Set ist ein praktisches Zubehör für Krankenschwestern, Ärzte, medizinisches Personal und Studenten im Gesundheitsbereich. Mit diesem Set erhalten Sie 2 Lanyards in den Farben Weiß und Türkis. Die Schnur hat eine Länge von 48,5 cm und ist mit einem stabilen Metallclip ausgestattet, der eine einfache Befestigung an verschiedenen Gegenständen ermöglicht.\n\n\nDie Schlüsselanhänger bestehen aus hochwertigem Polyester-Material, das robust und dennoch leicht ist. Dadurch sind sie angenehm zu tragen und halten Ihren Schlüsselbund, Ausweis, Ihr Handy, Ihren USB-Stick und andere wichtige Gegenstände sicher an Ort und Stelle. Der Metallclip sorgt für eine sichere Befestigung und verhindert ein versehentliches Verlieren der Gegenstände.\n\n\nDas ansprechende Design der Lanyards ist mit verschiedenen Sanitärinstrument-Zeichnungen gestaltet. Diese machen die Schlüsselanhänger zu einem Blickfang im medizinischen Umfeld. Sie eignen sich perfekt für den Einsatz in Krankenhäusern, Kliniken, Arztpraxen und Schulen im Gesundheitsbereich.\n\n\nDie vielseitigen Funktionen der Lanyards ermöglichen Ihnen das praktische Aufhängen von Schlüsseln, Ausweisen, Handys, USB-Sticks und anderen wichtigen Utensilien. Sie sind ein unverzichtbares Zubehör für den täglichen Gebrauch und bieten Ihnen Komfort und Sicherheit.',
                                                                                                                'img_url': 'https://m.media-amazon.com/images/I/61Hy3pQtW1L.__AC_SY445_SX342_QL70_ML2_.jpg',
                                                                                                                'detail_seller_info': 'Impressum & Info zum Verkäufer\nBusiness-Verkäufer\nGeschäftsname: guangzhouxianqingdianzishangwuyouxiangongsi\nGeschäftsart: Unternehmen in Privatbesitz\nHandelsregisternummer: 91440118MACYADX93L\nUStID: DE366507233\nTelefonnummer: +8618640338878\nE-Mail: <EMAIL>\nGeschäftsadresse: \n长岗村井冚南路14号\n增城区\n永宁街道\n广州市\n511300\nCN\nDieser Verkäufer hat zugesichert, dass er alle geltenden Gesetze einhalten wird. Dazu gehört auch, dass er nur Produkte und Dienstleistungen anbietet, die mit den Richtlinien von Amazon und den geltenden Vorschriften des EU-Rechts übereinstimmen.',
                                                                                                                'site': 'de', 'asin': 'B0CW3B6Z9H', 'has_cart': 1,
                                                                                                                'url': 'https://www.amazon.de/dp/B0CW3B6Z9H'}, {'status': 10,
                                                                                                                                                                'title': '3 Stück Ausweishülle,Ymapinc Beidseitig Transparenter Ausweishalter Fahrkartenhülle Plastik Kartenhalter Ausweis Card Wallet Schwarzer Badge Holder Verwendet für Schulausweis Kreditkarte Führerschein',
                                                                                                                                                                'platform_account': 'dreampursuer',
                                                                                                                                                                'tech_spec': {
                                                                                                                                                                    'Hersteller': '\u200eYmapinc',
                                                                                                                                                                    'Marke': '\u200eYmapinc',
                                                                                                                                                                    'Modellnummer': '\u200e592818_1_shcaBUWaT',
                                                                                                                                                                    'Produktabmessungen': '\u200e11,2 x 7 x 0,55 cm; 50 Gramm',
                                                                                                                                                                    'Farbe': '\u200eSchwarz',
                                                                                                                                                                    'Verschluss': '\u200eDruckknopf',
                                                                                                                                                                    'Material': '\u200ePVC-Kunststoff',
                                                                                                                                                                    'Produktanzahl': '\u200e3',
                                                                                                                                                                    'Herstellernummer': '\u200e592818_1_rwceCudM',
                                                                                                                                                                    'Artikelgewicht': '\u200e50 g'},
                                                                                                                                                                'product_desc': 'Produktbeschreibungen \nSpezifikation Informationen:\n\nName ：3pcs transparent Arbeit Kartenhalter\n\nFarbe ：Schwarz\n\nMaterial: Plastik\n\nProduktgröße (cm): 11.2*7*0.55cm\n\nProduktgröße (Zoll): 4.41*2.76*0.22inch\n\nNettogewicht: 48.5g\n\nBruttogewicht: 50.5g\n\nVerpackung: Tasche\n\nVerpackungsgröße: 18*10*1.8cm\n\nPackliste: 3pcs schwarz transparent Arbeit Kartenhalter',
                                                                                                                                                                'img_url': 'https://m.media-amazon.com/images/I/61dIay0CatL.__AC_SX300_SY300_QL70_ML2_.jpg',
                                                                                                                                                                'detail_seller_info': 'Impressum & Info zum Verkäufer\nBusiness-Verkäufer\nGeschäftsname: shenzhenshibubufakejiyouxiangongsi\nGeschäftsart: Unternehmen in Privatbesitz\nHandelsregisternummer: 91440300MAD1FD965F\nUStID: DE367153764\nTelefonnummer: +8615813897509\nE-Mail: <EMAIL>\nGeschäftsadresse: \n平湖街道上木古社区\n新木路328号1712\n深圳市\n龙岗区\n广东\n518111\nCN\nDieser Verkäufer hat zugesichert, dass er alle geltenden Gesetze einhalten wird. Dazu gehört auch, dass er nur Produkte und Dienstleistungen anbietet, die mit den Richtlinien von Amazon und den geltenden Vorschriften des EU-Rechts übereinstimmen.',
                                                                                                                                                                'site': 'de',
                                                                                                                                                                'asin': 'B0D9426M9H',
                                                                                                                                                                'has_cart': 1,
                                                                                                                                                                'url': 'https://www.amazon.de/dp/B0D9426M9H'}]
    params = {'app_id': 12, 'create_time': 1741261601, 'data_status': 1, 'date_time': 1741190400, 'done_time': 1741261601, 'platform_num': 5, 'result': '成功', 'site': 'de',
              'status': 10, 'task_id': 84, 'task_num': 5, 'task_time': None, 'user_id': 0, 'username': 'zc'}
    insert_amazon_listing(data, params, 'rpa.data_amazon_asin_attr')
