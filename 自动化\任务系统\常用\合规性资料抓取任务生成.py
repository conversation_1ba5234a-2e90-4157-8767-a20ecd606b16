# -*- coding:UTF-8 -*-
# @FileName  :合规性资料抓取任务生成.py
# @Time      :2024/9/12 19:40
# <AUTHOR>
from utils_mrc.MysqlHelper import *

MS = MSXS
title = contents = 'AM_GCM况志强,A_MKsg,AM_FJB冯建波-转主体'

MS.insert("""
INSERT INTO `rpa`.`task_amazon_rule` (`app_id`, `platform`, `title`, `contents`, `user_id`, `username`, `start_time`, `end_time`, `datetime`, `create_time`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
""", (9, 'amazon', title, contents, 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int(),))
print(MS.err) if MS.err else print('抓取任务创建成功！')
