import random
import requests
import export_tk
import hashlib


# Assume C_.version() is a coroutine function
# class C_:
#     @staticmethod
#     async def version():
#         return {'version': '4.5.0'}

# Your TokenGenerator class
class Login:
    @staticmethod
    def login_email(email, password, tk, version, language, extension, source):
        url = f"https://www.sellersprite.com/v2/extension/signin?email={email}&password={password}&tk={tk}&version={version}&language={language}&extension={extension}&source={source}"

        headers = {
            'Host': 'www.sellersprite.com',
            'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Microsoft Edge";v="122"',
            'accept': 'application/json',
            'sec-ch-ua-mobile': '?0',
            'random-token': "26754b55-389a-4742-8106-13f223fae2d4c",
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-site': 'none',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'accept-language': 'zh-CN,zh;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'Connection': 'keep-alive'
        }

        response = requests.request(method='GET', url=url, headers=headers)

        return response.json()

    @staticmethod
    def generate_uuid():
        return "{:08x}-{:04x}-4{:03x}-{:03x}-{:012x}".format(
                random.getrandbits(32),
                random.getrandbits(16),
                random.getrandbits(12) | 0x4000,
                random.getrandbits(12) | 0x8000,
                random.getrandbits(48)
        )


def md5_lower(data):
    md5_hash = hashlib.md5()
    md5_hash.update(data.encode('utf-8'))
    md5_result = md5_hash.hexdigest()
    return md5_result


def task():
    account = "yxylll"
    password = "yxylili.."

    tk = export_tk.login(account, md5_lower(password))
    print(tk)

    # Use tk in login_email
    result = Login.login_email(account, md5_lower(password), tk, "4.5.0", "zh_CN",
                               "libkfdihmladjiijocodhhkkkbjiadpd", "offline")
    print(result)
    if result['code'] == "OK":
        print('登录成功！')
        token = result['data']['token']
        print(token)


if __name__ == '__main__':
    task()
